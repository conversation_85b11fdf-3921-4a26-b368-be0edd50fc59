import 'package:flutter/material.dart';

class AppColors {
  // Primary colors
  static const Color primary = Color(0xFFFF2840);
  // Color.fromARGB(255, 63, 181, 163);
  static const Color primaryAverage = Color(0xFFCC1626);
  static const Color primaryLight = Color.fromARGB(255, 127, 226, 219);
  static const Color primaryDark = Color(0xFF99050C);
  // Color.fromARGB(255, 30, 159, 92);

  // Secondary colors
  static const Color secondary = Color(0xFFFF4081);
  static const Color secondaryLight = Color(0xFFFF80AB);
  static const Color secondaryDark = Color(0xFFC51162);

  // Background colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);

  // Text colors
  static const Color textPrimary = Color(0xFF1A2632);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textGrey = Color(0xFF6B7280);

  // Status colors
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFF44336);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);
  static const Color white = Color(0xFFFFFFFF);

  // Order status colors
  static const Color orderPending = Color(0xFFFF9800); // Orange
  static const Color orderConfirmed = Color(0xFF2196F3); // Blue
  static const Color orderPreparing = Color(0xFFFFC107); // Amber
  static const Color orderOutForDelivery = Color(0xFF9C27B0); // Purple

  //Shadow colors
  static const Color shadowGrey = Color(0x1A00001A);
}
