class TextFormatter {
  static String formatToUiText(String text) {
    if (text.isEmpty) return '';
    return text
        .split(RegExp(r'[-_]'))
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }
}

class DateTimeUtils {
  static bool isSameDate(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
