class AppValidator {
  static ValidationState emailValidator(String? email) {
    String value = email?.trim() ?? "";
    if (value.isEmpty) {
      return ValidationState(false, message: "Please enter the email address");
    } else if (!RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(value)) {
      return ValidationState(false,
          message: "Please enter valid email address");
    } else {
      return ValidationState(true);
    }
  }

  static ValidationState nameValidator(
    String? name,
    String? message,
  ) {
    String value = name?.trim() ?? "";
    if (value.isEmpty) {
      return ValidationState(false, message: message);
    } else if (!RegExp(r"^(?=[a-z A-Z._]{3,30}$)(?!.*[_.]{2})[^_.].*[^_.]$")
        .hasMatch(value)) {
      return ValidationState(false, message: "Please enter valid name");
    } else {
      return ValidationState(true);
    }
  }

  static ValidationState numberValidator(
    num? number, {
    String? nullMessage,
    String? emptyMessage,
    num? maxValue,
    String? maxValueMessage,
  }) {
    if (number == null) {
      return ValidationState(false,
          message: nullMessage ?? "Please enter a valid number");
    } else if (number <= 0) {
      return ValidationState(false,
          message: emptyMessage ?? "Please enter a number greater than 0");
    } else if ((maxValue != null) && (number > maxValue)) {
      return ValidationState(false,
          message:
              maxValueMessage ?? "Please enter a number less than $maxValue");
    } else {
      return ValidationState(true);
    }
  }

  static ValidationState passwordValidator(String? password,
      {bool checkStrength = true, String? message}) {
    String value = password?.trim() ?? "";
    if (value.isEmpty) {
      return ValidationState(false,
          message: message ?? "Please enter the password");
    } else if (checkStrength &&
        !RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$')
            .hasMatch(value)) {
      return ValidationState(false,
          message:
              "Password minimum six characters, at least one uppercase letter, one lowercase letter, one number and one special character");
    } else {
      return ValidationState(true);
    }
  }

  static ValidationState passwordMatchValidator(
      String? newPassword, String? confirmPassword) {
    String password = newPassword?.trim() ?? "";
    String confirmPasswordValue = confirmPassword?.trim() ?? "";
    if (password.isEmpty) {
      return ValidationState(false, message: "Please enter the password");
    } else if (confirmPasswordValue.isEmpty) {
      return ValidationState(false,
          message: "Please enter the confirm password");
    } else if (password != confirmPasswordValue) {
      return ValidationState(false, message: "Password must be same");
    } else {
      return ValidationState(true);
    }
  }

  static ValidationState mobileNumberValidator(
    String? numbers,
  ) {
    String number = numbers?.trim() ?? "";
    int length = 10;
    if (number.isEmpty) {
      return ValidationState(false, message: "Please enter the mobile number");
    } else if (number.length != length) {
      return ValidationState(false,
          message: "Please enter a valid $length digit mobile number");
    } else {
      return ValidationState(true);
    }
  }

  static ValidationState otpValidator(
    String? numbers,
  ) {
    String number = numbers?.trim() ?? "";
    int length = 6;
    if (number.isEmpty) {
      return ValidationState(false, message: "Please enter the OTP");
    } else if (number.length != length) {
      return ValidationState(false, message: "Please enter a valid OTP");
    } else {
      return ValidationState(true);
    }
  }

  static ValidationState emptyStringValidator(
    String? text,
    String? message, {
    int? minLength,
    String? lengthMessage,
  }) {
    String value = text?.trim() ?? "";
    if (value.isEmpty) {
      return ValidationState(false, message: message);
    } else if (minLength != null && value.length < minLength) {
      return ValidationState(false, message: lengthMessage);
    } else {
      return ValidationState(true);
    }
  }

  static ValidationState nullStringValidator(
    String? text,
    String? nullMessage,
    String? emptyMessage, {
    int? minLength,
    String? lengthMessage,
  }) {
    String? value = text?.trim();
    if (value == null) {
      return ValidationState(false, message: nullMessage);
    } else if (value.isEmpty) {
      return ValidationState(false,
          message: emptyMessage ?? "please enter valid input");
    } else if (minLength != null && value.length < minLength) {
      return ValidationState(false, message: lengthMessage);
    } else {
      return ValidationState(true);
    }
  }

  static ValidationState emptyListValidator(
    List? list,
    String? message,
  ) {
    if ((list ?? []).isEmpty) {
      return ValidationState(false, message: message);
    } else {
      return ValidationState(true);
    }
  }

  static ValidationState invalidStringValidator(
    String? text,
    String? message, {
    String? invalidMessage,
  }) {
    String value = text?.trim() ?? "";
    if (value.isEmpty) {
      return ValidationState(false, message: message);
    } else if (!RegExp(".*[a-zA-Z].*").hasMatch(value)) {
      return ValidationState(false,
          message: invalidMessage ?? "Please enter valid input");
    } else {
      return ValidationState(true);
    }
  }

  static bool modelValidator(List<ValidationState> items) {
    for (ValidationState item in items) {
      if (!item.valid) {
        // AppAlerts.customSnackBar(item.message ?? "Please enter valid input");
        return false;
      }
    }
    return true;
  }
}

class ValidationState {
  final bool valid;
  final String? message;

  ValidationState(this.valid, {this.message});
}
