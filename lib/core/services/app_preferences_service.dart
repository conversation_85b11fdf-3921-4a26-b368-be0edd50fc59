import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppPreferences {
  static late SharedPreferences _preferences;
  static const String isUserLoggedIn = 'isuserloggedin';
  static const String userToken = 'token';
  static const String userData = 'userData';
  static const String fcmToken = 'fcmToken';
  static const String themeMode = 'themeMode';
  static const String userType = 'userType';
  static const String recentSearches = 'recentSearches';
  static const String cartData = 'cart_data';

  static Future init() async {
    _preferences = await SharedPreferences.getInstance();
  }

  static Future setLoginStatus(bool loginStatus) async {
    await _preferences.setBool(isUserLoggedIn, loginStatus);
  }

  static bool? getLoginStatus() {
    return _preferences.getBool(isUserLoggedIn);
  }

  static Future setUserType(String type) async {
    await _preferences.setString(userType, type);
  }

  static String? getUserType() {
    return _preferences.getString(userType);
  }

  static Future setUserdata(String userJsonData) async {
    await _preferences.setString(userData, userJsonData);
  }

  static String? getUserdata() {
    return (_preferences.getString(userData));
  }

  static Future setfcmToken(String? token) async {
    await _preferences.setString(fcmToken, token!);
  }

  static String? getfcmToken() {
    return (_preferences.getString(fcmToken));
  }

  static Future setToken(String token) async {
    await _preferences.setString(userToken, token);
  }

  static String? getToken() {
    return _preferences.getString(userToken);
  }

  static Future setTheme(String? theme) async {
    await _preferences.setString(themeMode, theme ?? ThemeMode.system.name);
  }

  static String? getTheme() {
    return (_preferences.getString(themeMode));
  }

  static Future setRecentSearches(String data) async {
    await _preferences.setString(recentSearches, data);
  }

  static String? getRecentSearches() {
    return _preferences.getString(recentSearches);
  }

  static Future setCartData(String data) async {
    await _preferences.setString(cartData, data);
  }

  static String? getCartData() {
    return _preferences.getString(cartData);
  }

  static Future<bool> clearOne(String key) async {
    return _preferences.remove(key);
  }

  static Future<bool> clearAllData() async {
    return _preferences.clear();
  }
}
