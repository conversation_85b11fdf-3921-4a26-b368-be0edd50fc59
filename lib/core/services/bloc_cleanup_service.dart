import 'package:flutter/foundation.dart';
import '../dependency_injection/di_container.dart';
import '../../features/cart/bloc/cart_bloc.dart';
import '../../features/location/bloc/location%20bloc/location_bloc.dart';

/// Service to handle BLoC cleanup and reset operations
/// This helps manage memory and state when users log out or app restarts
class BlocCleanupService {
  /// Reset all user-specific BLoCs to their initial state
  /// Call this when user logs out to prevent data leakage between users
  static void resetUserSpecificBlocs() {
    try {
      // Reset CartBloc - clear all cart items and user-specific data
      final cartBloc = getIt<CartBloc>();
      cartBloc.resetCart();

      // Reset LocationBloc - clear user-specific location data
      final locationBloc = getIt<LocationBloc>();
      locationBloc.resetLocation();

      // Note: ThemeBloc, LanguageBloc, and WebViewBloc are global state
      // and should not be reset on user logout
    } catch (e) {
      // Log error but don't throw to prevent app crashes
      debugPrint('Error resetting BLoCs: $e');
    }
  }

  /// Dispose all BLoCs - use this only when app is being terminated
  /// This is mainly for testing or complete app shutdown
  static Future<void> disposeAllBlocs() async {
    try {
      // Note: In production, GetIt singletons are typically not disposed
      // unless the entire app is shutting down

      // For testing purposes, you might want to reset GetIt
      // getIt.reset(); // Uncomment only for testing
    } catch (e) {
      debugPrint('Error disposing BLoCs: $e');
    }
  }

  /// Check if all critical BLoCs are properly initialized
  static bool areBloCsInitialized() {
    try {
      getIt<CartBloc>();
      getIt<LocationBloc>();
      return true;
    } catch (e) {
      return false;
    }
  }
}
