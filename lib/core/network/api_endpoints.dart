import '../config/environment_config.dart';

/// Base URL configuration using environment variables
/// ⚠️ SECURITY: URLs are now loaded from environment configuration
class BaseUrl {
  /// Get the base URL for the current environment
  /// This is now loaded from environment variables for security
  static String get baseUrl => EnvironmentConfig.apiBaseUrl;

  /// Legacy support - deprecated, use baseUrl instead
  @Deprecated('Use baseUrl instead. This will be removed in future versions.')
  static String get localHostUrl => "";

  @Deprecated('Use EnvironmentConfig.apiBaseUrl instead')
  static String get devUrl => EnvironmentConfig.apiBaseUrl;

  @Deprecated('Use EnvironmentConfig.apiBaseUrl instead')
  static String get stagingUrl => EnvironmentConfig.apiBaseUrl;

  @Deprecated('Use EnvironmentConfig.apiBaseUrl instead')
  static String get productionUrl => EnvironmentConfig.apiBaseUrl;
}

class EndUrl {
  //auth
  static const String loginUser = 'api/sign-in';
  static const String registerUser = 'api/register';
  static const String forgotPassword = 'api/forgot-password';

  //orders
  static const String createOrder = 'create_order';
  static const String getOrderHistory = 'api/orders/history';
  static const String getOrderDetails = 'api/orders'; // append /{orderId}
  static const String cancelOrder = 'api/orders'; // append /{orderId}/cancel
}
