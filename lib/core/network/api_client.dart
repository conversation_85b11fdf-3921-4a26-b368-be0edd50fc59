import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:rozana/app/app_config.dart';

import '../utils/logger.dart';
import 'dio_interceptors.dart';

enum HttpMethod { get, post, put, patch, delete }

class ApiClient {
  static String getMethod(HttpMethod method) {
    switch (method) {
      case HttpMethod.get:
        return 'GET';
      case HttpMethod.post:
        return 'POST';
      case HttpMethod.put:
        return 'PUT';
      case HttpMethod.patch:
        return 'PATCH';
      case HttpMethod.delete:
        return 'DELETE';
    }
  }

  static Map<String, dynamic> sendFailureResponse(String msg) {
    return {"success": false, "message": msg, "data": null};
  }

  static Future<bool> _isNetworkAvailable() async {
    final connectivityResults = await Connectivity().checkConnectivity();

    if (connectivityResults.contains(ConnectivityResult.none)) {
      LogMessage.p('No internet connection available.');
      return false;
    }

    LogMessage.p('Connected to: $connectivityResults');
    return true; // Network is available
  }

  static Future<Response<dynamic>?> sendHttpRequest({
    required String endUrl,
    required HttpMethod method,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    String? tag,
  }) async {
    final dio = Dio();
    dio.options
      ..baseUrl = AppConfig.baseUrl
      ..connectTimeout = const Duration(seconds: 30)
      ..receiveTimeout = const Duration(seconds: 30)
      ..headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      };

    dio.interceptors.addAll([
      TokenInterceptor(),
      LoggingInterceptor(tag: tag != null ? 'API::$tag' : "API::")
    ]);

    if (!await _isNetworkAvailable()) {
      return Response(
        requestOptions: RequestOptions(),
        data: sendFailureResponse(ApiErrorMessages.connectionFailure),
      );
    }

    try {
      final response = await dio.request(
        endUrl,
        data: data ?? {},
        queryParameters: queryParameters,
        cancelToken: cancelToken,
        options: Options(method: getMethod(method)),
      );
      return response;
    } catch (e) {
      LogMessage.p('API Error: $e');
      throw DioExceptions.fromDioError(e as DioException);
    }
  }
}

// ======================= Error Handling =======================
class DioExceptions implements Exception {
  String message = "";

  DioExceptions.fromDioError(DioException dioError) {
    switch (dioError.type) {
      case DioExceptionType.cancel:
        message = ApiErrorMessages.requestCancelled;
        break;
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.sendTimeout:
        message = ApiErrorMessages.connectionTimeout;
        break;
      case DioExceptionType.unknown:
        message = ApiErrorMessages.somethingWentWrong;
        break;
      case DioExceptionType.badResponse:
        message = _handleError(dioError.response);
        break;
      default:
        message = ApiErrorMessages.somethingWentWrong;
        break;
    }
  }

  String _handleError(Response<dynamic>? response) {
    int statusCode = response?.statusCode ?? 0;
    String? message = response?.data?['message']?.toString();
    switch (statusCode) {
      case 400:
        return message ?? ApiErrorMessages.badRequest;
      case 401:
        return message ?? ApiErrorMessages.unAuthorized;
      case 404:
        return message ?? ApiErrorMessages.notFound;
      case 500:
        return message ?? ApiErrorMessages.serverError;
      default:
        return message ?? ApiErrorMessages.somethingWentWrong;
    }
  }

  @override
  String toString() => message;
}

// ======================= Error Handling =======================
class ApiErrorMessages {
  static String connectionTimeout = "Connection timed out";
  static String somethingWentWrong = "Oops something went wrong";
  static String connectionFailure = "Please check the network connection";
  static String requestCancelled = "Request to api server was cancelled";
  static String badRequest = 'Bad request';
  static String notFound = "Not Found";
  static String serverError = 'Internal server error';
  static String unAuthorized = 'Unauthorized';
}
