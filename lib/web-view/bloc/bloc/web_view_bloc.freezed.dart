// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'web_view_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WebViewEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() contentLoaded,
    required TResult Function(String error) contentFailed,
    required TResult Function() switchToWebView,
    required TResult Function() switchToNativeView,
    required TResult Function(double width) checkScreenWidth,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? contentLoaded,
    TResult? Function(String error)? contentFailed,
    TResult? Function()? switchToWebView,
    TResult? Function()? switchToNativeView,
    TResult? Function(double width)? checkScreenWidth,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? contentLoaded,
    TResult Function(String error)? contentFailed,
    TResult Function()? switchToWebView,
    TResult Function()? switchToNativeView,
    TResult Function(double width)? checkScreenWidth,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_ContentLoaded value) contentLoaded,
    required TResult Function(_ContentFailed value) contentFailed,
    required TResult Function(_SwitchToWebView value) switchToWebView,
    required TResult Function(_SwitchToNativeView value) switchToNativeView,
    required TResult Function(_CheckScreenWidth value) checkScreenWidth,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_ContentLoaded value)? contentLoaded,
    TResult? Function(_ContentFailed value)? contentFailed,
    TResult? Function(_SwitchToWebView value)? switchToWebView,
    TResult? Function(_SwitchToNativeView value)? switchToNativeView,
    TResult? Function(_CheckScreenWidth value)? checkScreenWidth,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_ContentLoaded value)? contentLoaded,
    TResult Function(_ContentFailed value)? contentFailed,
    TResult Function(_SwitchToWebView value)? switchToWebView,
    TResult Function(_SwitchToNativeView value)? switchToNativeView,
    TResult Function(_CheckScreenWidth value)? checkScreenWidth,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebViewEventCopyWith<$Res> {
  factory $WebViewEventCopyWith(
          WebViewEvent value, $Res Function(WebViewEvent) then) =
      _$WebViewEventCopyWithImpl<$Res, WebViewEvent>;
}

/// @nodoc
class _$WebViewEventCopyWithImpl<$Res, $Val extends WebViewEvent>
    implements $WebViewEventCopyWith<$Res> {
  _$WebViewEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$WebViewEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'WebViewEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() contentLoaded,
    required TResult Function(String error) contentFailed,
    required TResult Function() switchToWebView,
    required TResult Function() switchToNativeView,
    required TResult Function(double width) checkScreenWidth,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? contentLoaded,
    TResult? Function(String error)? contentFailed,
    TResult? Function()? switchToWebView,
    TResult? Function()? switchToNativeView,
    TResult? Function(double width)? checkScreenWidth,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? contentLoaded,
    TResult Function(String error)? contentFailed,
    TResult Function()? switchToWebView,
    TResult Function()? switchToNativeView,
    TResult Function(double width)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_ContentLoaded value) contentLoaded,
    required TResult Function(_ContentFailed value) contentFailed,
    required TResult Function(_SwitchToWebView value) switchToWebView,
    required TResult Function(_SwitchToNativeView value) switchToNativeView,
    required TResult Function(_CheckScreenWidth value) checkScreenWidth,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_ContentLoaded value)? contentLoaded,
    TResult? Function(_ContentFailed value)? contentFailed,
    TResult? Function(_SwitchToWebView value)? switchToWebView,
    TResult? Function(_SwitchToNativeView value)? switchToNativeView,
    TResult? Function(_CheckScreenWidth value)? checkScreenWidth,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_ContentLoaded value)? contentLoaded,
    TResult Function(_ContentFailed value)? contentFailed,
    TResult Function(_SwitchToWebView value)? switchToWebView,
    TResult Function(_SwitchToNativeView value)? switchToNativeView,
    TResult Function(_CheckScreenWidth value)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements WebViewEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
abstract class _$$ContentLoadedImplCopyWith<$Res> {
  factory _$$ContentLoadedImplCopyWith(
          _$ContentLoadedImpl value, $Res Function(_$ContentLoadedImpl) then) =
      __$$ContentLoadedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ContentLoadedImplCopyWithImpl<$Res>
    extends _$WebViewEventCopyWithImpl<$Res, _$ContentLoadedImpl>
    implements _$$ContentLoadedImplCopyWith<$Res> {
  __$$ContentLoadedImplCopyWithImpl(
      _$ContentLoadedImpl _value, $Res Function(_$ContentLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ContentLoadedImpl implements _ContentLoaded {
  const _$ContentLoadedImpl();

  @override
  String toString() {
    return 'WebViewEvent.contentLoaded()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ContentLoadedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() contentLoaded,
    required TResult Function(String error) contentFailed,
    required TResult Function() switchToWebView,
    required TResult Function() switchToNativeView,
    required TResult Function(double width) checkScreenWidth,
  }) {
    return contentLoaded();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? contentLoaded,
    TResult? Function(String error)? contentFailed,
    TResult? Function()? switchToWebView,
    TResult? Function()? switchToNativeView,
    TResult? Function(double width)? checkScreenWidth,
  }) {
    return contentLoaded?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? contentLoaded,
    TResult Function(String error)? contentFailed,
    TResult Function()? switchToWebView,
    TResult Function()? switchToNativeView,
    TResult Function(double width)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (contentLoaded != null) {
      return contentLoaded();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_ContentLoaded value) contentLoaded,
    required TResult Function(_ContentFailed value) contentFailed,
    required TResult Function(_SwitchToWebView value) switchToWebView,
    required TResult Function(_SwitchToNativeView value) switchToNativeView,
    required TResult Function(_CheckScreenWidth value) checkScreenWidth,
  }) {
    return contentLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_ContentLoaded value)? contentLoaded,
    TResult? Function(_ContentFailed value)? contentFailed,
    TResult? Function(_SwitchToWebView value)? switchToWebView,
    TResult? Function(_SwitchToNativeView value)? switchToNativeView,
    TResult? Function(_CheckScreenWidth value)? checkScreenWidth,
  }) {
    return contentLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_ContentLoaded value)? contentLoaded,
    TResult Function(_ContentFailed value)? contentFailed,
    TResult Function(_SwitchToWebView value)? switchToWebView,
    TResult Function(_SwitchToNativeView value)? switchToNativeView,
    TResult Function(_CheckScreenWidth value)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (contentLoaded != null) {
      return contentLoaded(this);
    }
    return orElse();
  }
}

abstract class _ContentLoaded implements WebViewEvent {
  const factory _ContentLoaded() = _$ContentLoadedImpl;
}

/// @nodoc
abstract class _$$ContentFailedImplCopyWith<$Res> {
  factory _$$ContentFailedImplCopyWith(
          _$ContentFailedImpl value, $Res Function(_$ContentFailedImpl) then) =
      __$$ContentFailedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String error});
}

/// @nodoc
class __$$ContentFailedImplCopyWithImpl<$Res>
    extends _$WebViewEventCopyWithImpl<$Res, _$ContentFailedImpl>
    implements _$$ContentFailedImplCopyWith<$Res> {
  __$$ContentFailedImplCopyWithImpl(
      _$ContentFailedImpl _value, $Res Function(_$ContentFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$ContentFailedImpl(
      null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ContentFailedImpl implements _ContentFailed {
  const _$ContentFailedImpl(this.error);

  @override
  final String error;

  @override
  String toString() {
    return 'WebViewEvent.contentFailed(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentFailedImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentFailedImplCopyWith<_$ContentFailedImpl> get copyWith =>
      __$$ContentFailedImplCopyWithImpl<_$ContentFailedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() contentLoaded,
    required TResult Function(String error) contentFailed,
    required TResult Function() switchToWebView,
    required TResult Function() switchToNativeView,
    required TResult Function(double width) checkScreenWidth,
  }) {
    return contentFailed(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? contentLoaded,
    TResult? Function(String error)? contentFailed,
    TResult? Function()? switchToWebView,
    TResult? Function()? switchToNativeView,
    TResult? Function(double width)? checkScreenWidth,
  }) {
    return contentFailed?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? contentLoaded,
    TResult Function(String error)? contentFailed,
    TResult Function()? switchToWebView,
    TResult Function()? switchToNativeView,
    TResult Function(double width)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (contentFailed != null) {
      return contentFailed(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_ContentLoaded value) contentLoaded,
    required TResult Function(_ContentFailed value) contentFailed,
    required TResult Function(_SwitchToWebView value) switchToWebView,
    required TResult Function(_SwitchToNativeView value) switchToNativeView,
    required TResult Function(_CheckScreenWidth value) checkScreenWidth,
  }) {
    return contentFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_ContentLoaded value)? contentLoaded,
    TResult? Function(_ContentFailed value)? contentFailed,
    TResult? Function(_SwitchToWebView value)? switchToWebView,
    TResult? Function(_SwitchToNativeView value)? switchToNativeView,
    TResult? Function(_CheckScreenWidth value)? checkScreenWidth,
  }) {
    return contentFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_ContentLoaded value)? contentLoaded,
    TResult Function(_ContentFailed value)? contentFailed,
    TResult Function(_SwitchToWebView value)? switchToWebView,
    TResult Function(_SwitchToNativeView value)? switchToNativeView,
    TResult Function(_CheckScreenWidth value)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (contentFailed != null) {
      return contentFailed(this);
    }
    return orElse();
  }
}

abstract class _ContentFailed implements WebViewEvent {
  const factory _ContentFailed(final String error) = _$ContentFailedImpl;

  String get error;

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContentFailedImplCopyWith<_$ContentFailedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SwitchToWebViewImplCopyWith<$Res> {
  factory _$$SwitchToWebViewImplCopyWith(_$SwitchToWebViewImpl value,
          $Res Function(_$SwitchToWebViewImpl) then) =
      __$$SwitchToWebViewImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SwitchToWebViewImplCopyWithImpl<$Res>
    extends _$WebViewEventCopyWithImpl<$Res, _$SwitchToWebViewImpl>
    implements _$$SwitchToWebViewImplCopyWith<$Res> {
  __$$SwitchToWebViewImplCopyWithImpl(
      _$SwitchToWebViewImpl _value, $Res Function(_$SwitchToWebViewImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SwitchToWebViewImpl implements _SwitchToWebView {
  const _$SwitchToWebViewImpl();

  @override
  String toString() {
    return 'WebViewEvent.switchToWebView()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SwitchToWebViewImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() contentLoaded,
    required TResult Function(String error) contentFailed,
    required TResult Function() switchToWebView,
    required TResult Function() switchToNativeView,
    required TResult Function(double width) checkScreenWidth,
  }) {
    return switchToWebView();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? contentLoaded,
    TResult? Function(String error)? contentFailed,
    TResult? Function()? switchToWebView,
    TResult? Function()? switchToNativeView,
    TResult? Function(double width)? checkScreenWidth,
  }) {
    return switchToWebView?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? contentLoaded,
    TResult Function(String error)? contentFailed,
    TResult Function()? switchToWebView,
    TResult Function()? switchToNativeView,
    TResult Function(double width)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (switchToWebView != null) {
      return switchToWebView();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_ContentLoaded value) contentLoaded,
    required TResult Function(_ContentFailed value) contentFailed,
    required TResult Function(_SwitchToWebView value) switchToWebView,
    required TResult Function(_SwitchToNativeView value) switchToNativeView,
    required TResult Function(_CheckScreenWidth value) checkScreenWidth,
  }) {
    return switchToWebView(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_ContentLoaded value)? contentLoaded,
    TResult? Function(_ContentFailed value)? contentFailed,
    TResult? Function(_SwitchToWebView value)? switchToWebView,
    TResult? Function(_SwitchToNativeView value)? switchToNativeView,
    TResult? Function(_CheckScreenWidth value)? checkScreenWidth,
  }) {
    return switchToWebView?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_ContentLoaded value)? contentLoaded,
    TResult Function(_ContentFailed value)? contentFailed,
    TResult Function(_SwitchToWebView value)? switchToWebView,
    TResult Function(_SwitchToNativeView value)? switchToNativeView,
    TResult Function(_CheckScreenWidth value)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (switchToWebView != null) {
      return switchToWebView(this);
    }
    return orElse();
  }
}

abstract class _SwitchToWebView implements WebViewEvent {
  const factory _SwitchToWebView() = _$SwitchToWebViewImpl;
}

/// @nodoc
abstract class _$$SwitchToNativeViewImplCopyWith<$Res> {
  factory _$$SwitchToNativeViewImplCopyWith(_$SwitchToNativeViewImpl value,
          $Res Function(_$SwitchToNativeViewImpl) then) =
      __$$SwitchToNativeViewImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SwitchToNativeViewImplCopyWithImpl<$Res>
    extends _$WebViewEventCopyWithImpl<$Res, _$SwitchToNativeViewImpl>
    implements _$$SwitchToNativeViewImplCopyWith<$Res> {
  __$$SwitchToNativeViewImplCopyWithImpl(_$SwitchToNativeViewImpl _value,
      $Res Function(_$SwitchToNativeViewImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SwitchToNativeViewImpl implements _SwitchToNativeView {
  const _$SwitchToNativeViewImpl();

  @override
  String toString() {
    return 'WebViewEvent.switchToNativeView()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SwitchToNativeViewImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() contentLoaded,
    required TResult Function(String error) contentFailed,
    required TResult Function() switchToWebView,
    required TResult Function() switchToNativeView,
    required TResult Function(double width) checkScreenWidth,
  }) {
    return switchToNativeView();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? contentLoaded,
    TResult? Function(String error)? contentFailed,
    TResult? Function()? switchToWebView,
    TResult? Function()? switchToNativeView,
    TResult? Function(double width)? checkScreenWidth,
  }) {
    return switchToNativeView?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? contentLoaded,
    TResult Function(String error)? contentFailed,
    TResult Function()? switchToWebView,
    TResult Function()? switchToNativeView,
    TResult Function(double width)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (switchToNativeView != null) {
      return switchToNativeView();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_ContentLoaded value) contentLoaded,
    required TResult Function(_ContentFailed value) contentFailed,
    required TResult Function(_SwitchToWebView value) switchToWebView,
    required TResult Function(_SwitchToNativeView value) switchToNativeView,
    required TResult Function(_CheckScreenWidth value) checkScreenWidth,
  }) {
    return switchToNativeView(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_ContentLoaded value)? contentLoaded,
    TResult? Function(_ContentFailed value)? contentFailed,
    TResult? Function(_SwitchToWebView value)? switchToWebView,
    TResult? Function(_SwitchToNativeView value)? switchToNativeView,
    TResult? Function(_CheckScreenWidth value)? checkScreenWidth,
  }) {
    return switchToNativeView?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_ContentLoaded value)? contentLoaded,
    TResult Function(_ContentFailed value)? contentFailed,
    TResult Function(_SwitchToWebView value)? switchToWebView,
    TResult Function(_SwitchToNativeView value)? switchToNativeView,
    TResult Function(_CheckScreenWidth value)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (switchToNativeView != null) {
      return switchToNativeView(this);
    }
    return orElse();
  }
}

abstract class _SwitchToNativeView implements WebViewEvent {
  const factory _SwitchToNativeView() = _$SwitchToNativeViewImpl;
}

/// @nodoc
abstract class _$$CheckScreenWidthImplCopyWith<$Res> {
  factory _$$CheckScreenWidthImplCopyWith(_$CheckScreenWidthImpl value,
          $Res Function(_$CheckScreenWidthImpl) then) =
      __$$CheckScreenWidthImplCopyWithImpl<$Res>;
  @useResult
  $Res call({double width});
}

/// @nodoc
class __$$CheckScreenWidthImplCopyWithImpl<$Res>
    extends _$WebViewEventCopyWithImpl<$Res, _$CheckScreenWidthImpl>
    implements _$$CheckScreenWidthImplCopyWith<$Res> {
  __$$CheckScreenWidthImplCopyWithImpl(_$CheckScreenWidthImpl _value,
      $Res Function(_$CheckScreenWidthImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? width = null,
  }) {
    return _then(_$CheckScreenWidthImpl(
      null == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$CheckScreenWidthImpl implements _CheckScreenWidth {
  const _$CheckScreenWidthImpl(this.width);

  @override
  final double width;

  @override
  String toString() {
    return 'WebViewEvent.checkScreenWidth(width: $width)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckScreenWidthImpl &&
            (identical(other.width, width) || other.width == width));
  }

  @override
  int get hashCode => Object.hash(runtimeType, width);

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckScreenWidthImplCopyWith<_$CheckScreenWidthImpl> get copyWith =>
      __$$CheckScreenWidthImplCopyWithImpl<_$CheckScreenWidthImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() contentLoaded,
    required TResult Function(String error) contentFailed,
    required TResult Function() switchToWebView,
    required TResult Function() switchToNativeView,
    required TResult Function(double width) checkScreenWidth,
  }) {
    return checkScreenWidth(width);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? contentLoaded,
    TResult? Function(String error)? contentFailed,
    TResult? Function()? switchToWebView,
    TResult? Function()? switchToNativeView,
    TResult? Function(double width)? checkScreenWidth,
  }) {
    return checkScreenWidth?.call(width);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? contentLoaded,
    TResult Function(String error)? contentFailed,
    TResult Function()? switchToWebView,
    TResult Function()? switchToNativeView,
    TResult Function(double width)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (checkScreenWidth != null) {
      return checkScreenWidth(width);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_ContentLoaded value) contentLoaded,
    required TResult Function(_ContentFailed value) contentFailed,
    required TResult Function(_SwitchToWebView value) switchToWebView,
    required TResult Function(_SwitchToNativeView value) switchToNativeView,
    required TResult Function(_CheckScreenWidth value) checkScreenWidth,
  }) {
    return checkScreenWidth(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_ContentLoaded value)? contentLoaded,
    TResult? Function(_ContentFailed value)? contentFailed,
    TResult? Function(_SwitchToWebView value)? switchToWebView,
    TResult? Function(_SwitchToNativeView value)? switchToNativeView,
    TResult? Function(_CheckScreenWidth value)? checkScreenWidth,
  }) {
    return checkScreenWidth?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_ContentLoaded value)? contentLoaded,
    TResult Function(_ContentFailed value)? contentFailed,
    TResult Function(_SwitchToWebView value)? switchToWebView,
    TResult Function(_SwitchToNativeView value)? switchToNativeView,
    TResult Function(_CheckScreenWidth value)? checkScreenWidth,
    required TResult orElse(),
  }) {
    if (checkScreenWidth != null) {
      return checkScreenWidth(this);
    }
    return orElse();
  }
}

abstract class _CheckScreenWidth implements WebViewEvent {
  const factory _CheckScreenWidth(final double width) = _$CheckScreenWidthImpl;

  double get width;

  /// Create a copy of WebViewEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckScreenWidthImplCopyWith<_$CheckScreenWidthImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$WebViewState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loaded,
    required TResult Function(String message) error,
    required TResult Function() webViewMode,
    required TResult Function() nativeViewMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loaded,
    TResult? Function(String message)? error,
    TResult? Function()? webViewMode,
    TResult? Function()? nativeViewMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loaded,
    TResult Function(String message)? error,
    TResult Function()? webViewMode,
    TResult Function()? nativeViewMode,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_WebViewMode value) webViewMode,
    required TResult Function(_NativeViewMode value) nativeViewMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_WebViewMode value)? webViewMode,
    TResult? Function(_NativeViewMode value)? nativeViewMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_WebViewMode value)? webViewMode,
    TResult Function(_NativeViewMode value)? nativeViewMode,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebViewStateCopyWith<$Res> {
  factory $WebViewStateCopyWith(
          WebViewState value, $Res Function(WebViewState) then) =
      _$WebViewStateCopyWithImpl<$Res, WebViewState>;
}

/// @nodoc
class _$WebViewStateCopyWithImpl<$Res, $Val extends WebViewState>
    implements $WebViewStateCopyWith<$Res> {
  _$WebViewStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$WebViewStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'WebViewState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loaded,
    required TResult Function(String message) error,
    required TResult Function() webViewMode,
    required TResult Function() nativeViewMode,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loaded,
    TResult? Function(String message)? error,
    TResult? Function()? webViewMode,
    TResult? Function()? nativeViewMode,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loaded,
    TResult Function(String message)? error,
    TResult Function()? webViewMode,
    TResult Function()? nativeViewMode,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_WebViewMode value) webViewMode,
    required TResult Function(_NativeViewMode value) nativeViewMode,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_WebViewMode value)? webViewMode,
    TResult? Function(_NativeViewMode value)? nativeViewMode,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_WebViewMode value)? webViewMode,
    TResult Function(_NativeViewMode value)? nativeViewMode,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements WebViewState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$WebViewStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'WebViewState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loaded,
    required TResult Function(String message) error,
    required TResult Function() webViewMode,
    required TResult Function() nativeViewMode,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loaded,
    TResult? Function(String message)? error,
    TResult? Function()? webViewMode,
    TResult? Function()? nativeViewMode,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loaded,
    TResult Function(String message)? error,
    TResult Function()? webViewMode,
    TResult Function()? nativeViewMode,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_WebViewMode value) webViewMode,
    required TResult Function(_NativeViewMode value) nativeViewMode,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_WebViewMode value)? webViewMode,
    TResult? Function(_NativeViewMode value)? nativeViewMode,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_WebViewMode value)? webViewMode,
    TResult Function(_NativeViewMode value)? nativeViewMode,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements WebViewState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$WebViewStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl();

  @override
  String toString() {
    return 'WebViewState.loaded()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loaded,
    required TResult Function(String message) error,
    required TResult Function() webViewMode,
    required TResult Function() nativeViewMode,
  }) {
    return loaded();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loaded,
    TResult? Function(String message)? error,
    TResult? Function()? webViewMode,
    TResult? Function()? nativeViewMode,
  }) {
    return loaded?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loaded,
    TResult Function(String message)? error,
    TResult Function()? webViewMode,
    TResult Function()? nativeViewMode,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_WebViewMode value) webViewMode,
    required TResult Function(_NativeViewMode value) nativeViewMode,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_WebViewMode value)? webViewMode,
    TResult? Function(_NativeViewMode value)? nativeViewMode,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_WebViewMode value)? webViewMode,
    TResult Function(_NativeViewMode value)? nativeViewMode,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements WebViewState {
  const factory _Loaded() = _$LoadedImpl;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$WebViewStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'WebViewState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loaded,
    required TResult Function(String message) error,
    required TResult Function() webViewMode,
    required TResult Function() nativeViewMode,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loaded,
    TResult? Function(String message)? error,
    TResult? Function()? webViewMode,
    TResult? Function()? nativeViewMode,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loaded,
    TResult Function(String message)? error,
    TResult Function()? webViewMode,
    TResult Function()? nativeViewMode,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_WebViewMode value) webViewMode,
    required TResult Function(_NativeViewMode value) nativeViewMode,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_WebViewMode value)? webViewMode,
    TResult? Function(_NativeViewMode value)? nativeViewMode,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_WebViewMode value)? webViewMode,
    TResult Function(_NativeViewMode value)? nativeViewMode,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements WebViewState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$WebViewModeImplCopyWith<$Res> {
  factory _$$WebViewModeImplCopyWith(
          _$WebViewModeImpl value, $Res Function(_$WebViewModeImpl) then) =
      __$$WebViewModeImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WebViewModeImplCopyWithImpl<$Res>
    extends _$WebViewStateCopyWithImpl<$Res, _$WebViewModeImpl>
    implements _$$WebViewModeImplCopyWith<$Res> {
  __$$WebViewModeImplCopyWithImpl(
      _$WebViewModeImpl _value, $Res Function(_$WebViewModeImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WebViewModeImpl implements _WebViewMode {
  const _$WebViewModeImpl();

  @override
  String toString() {
    return 'WebViewState.webViewMode()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$WebViewModeImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loaded,
    required TResult Function(String message) error,
    required TResult Function() webViewMode,
    required TResult Function() nativeViewMode,
  }) {
    return webViewMode();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loaded,
    TResult? Function(String message)? error,
    TResult? Function()? webViewMode,
    TResult? Function()? nativeViewMode,
  }) {
    return webViewMode?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loaded,
    TResult Function(String message)? error,
    TResult Function()? webViewMode,
    TResult Function()? nativeViewMode,
    required TResult orElse(),
  }) {
    if (webViewMode != null) {
      return webViewMode();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_WebViewMode value) webViewMode,
    required TResult Function(_NativeViewMode value) nativeViewMode,
  }) {
    return webViewMode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_WebViewMode value)? webViewMode,
    TResult? Function(_NativeViewMode value)? nativeViewMode,
  }) {
    return webViewMode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_WebViewMode value)? webViewMode,
    TResult Function(_NativeViewMode value)? nativeViewMode,
    required TResult orElse(),
  }) {
    if (webViewMode != null) {
      return webViewMode(this);
    }
    return orElse();
  }
}

abstract class _WebViewMode implements WebViewState {
  const factory _WebViewMode() = _$WebViewModeImpl;
}

/// @nodoc
abstract class _$$NativeViewModeImplCopyWith<$Res> {
  factory _$$NativeViewModeImplCopyWith(_$NativeViewModeImpl value,
          $Res Function(_$NativeViewModeImpl) then) =
      __$$NativeViewModeImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NativeViewModeImplCopyWithImpl<$Res>
    extends _$WebViewStateCopyWithImpl<$Res, _$NativeViewModeImpl>
    implements _$$NativeViewModeImplCopyWith<$Res> {
  __$$NativeViewModeImplCopyWithImpl(
      _$NativeViewModeImpl _value, $Res Function(_$NativeViewModeImpl) _then)
      : super(_value, _then);

  /// Create a copy of WebViewState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NativeViewModeImpl implements _NativeViewMode {
  const _$NativeViewModeImpl();

  @override
  String toString() {
    return 'WebViewState.nativeViewMode()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NativeViewModeImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() loaded,
    required TResult Function(String message) error,
    required TResult Function() webViewMode,
    required TResult Function() nativeViewMode,
  }) {
    return nativeViewMode();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? loaded,
    TResult? Function(String message)? error,
    TResult? Function()? webViewMode,
    TResult? Function()? nativeViewMode,
  }) {
    return nativeViewMode?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? loaded,
    TResult Function(String message)? error,
    TResult Function()? webViewMode,
    TResult Function()? nativeViewMode,
    required TResult orElse(),
  }) {
    if (nativeViewMode != null) {
      return nativeViewMode();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
    required TResult Function(_WebViewMode value) webViewMode,
    required TResult Function(_NativeViewMode value) nativeViewMode,
  }) {
    return nativeViewMode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
    TResult? Function(_WebViewMode value)? webViewMode,
    TResult? Function(_NativeViewMode value)? nativeViewMode,
  }) {
    return nativeViewMode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    TResult Function(_WebViewMode value)? webViewMode,
    TResult Function(_NativeViewMode value)? nativeViewMode,
    required TResult orElse(),
  }) {
    if (nativeViewMode != null) {
      return nativeViewMode(this);
    }
    return orElse();
  }
}

abstract class _NativeViewMode implements WebViewState {
  const factory _NativeViewMode() = _$NativeViewModeImpl;
}
