import '../entities/banner_entity.dart';
import '../repositories/home_repository_interface.dart';

/// Use case for getting home banners
/// Encapsulates the business logic for retrieving banners
class GetBannersUseCase {
  final IHomeRepository _homeRepository;

  const GetBannersUseCase(this._homeRepository);

  /// Execute the use case to get banners
  /// 
  /// Returns a list of [BannerEntity]
  Future<List<BannerEntity>> execute() async {
    // Business logic can be added here
    // For example: validation, caching, filtering, etc.
    
    // Call repository to get data
    final banners = await _homeRepository.getBanners();
    
    // Apply business rules
    // For example: filter out banners with invalid image URLs
    return banners.where((banner) => 
      banner.imageUrl.isNotEmpty && 
      Uri.tryParse(banner.imageUrl) != null
    ).toList();
  }
}
