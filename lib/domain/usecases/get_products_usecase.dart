import '../entities/product_entity.dart';
import '../repositories/home_repository_interface.dart';

/// Use case for getting products
/// Encapsulates the business logic for retrieving products
class GetProductsUseCase {
  final IHomeRepository _homeRepository;

  const GetProductsUseCase(this._homeRepository);

  /// Execute the use case to get products
  ///
  /// [page] - Page number for pagination (0-based)
  /// [pageSize] - Number of items per page
  /// [refresh] - Whether to refresh the data
  /// [query] - Search query filter
  ///
  /// Returns a list of [ProductEntity]
  Future<List<ProductEntity>> execute({
    int page = 0,
    int pageSize = 9,
    bool refresh = false,
    String query = '',
  }) async {
    // Business logic can be added here
    // For example: validation, caching, filtering, etc.

    // Validate inputs
    if (page < 0) {
      throw ArgumentError('Page number cannot be negative');
    }

    if (pageSize <= 0) {
      throw ArgumentError('Page size must be greater than 0');
    }

    // Call repository to get data
    final products = await _homeRepository.getProducts(
      page: page,
      pageSize: pageSize,
      refresh: refresh,
      query: query,
    );

    // Apply business rules
    // For example: filter out out-of-stock products for certain queries
    if (query.toLowerCase() == 'available') {
      return products.where((product) => !product.isOutOfStock).toList();
    }

    // Sort by rating for 'popular' query
    if (query.toLowerCase() == 'popular') {
      final sortedProducts = List<ProductEntity>.from(products);
      sortedProducts.sort((a, b) => b.rating.compareTo(a.rating));
      return sortedProducts;
    }

    return products;
  }
}
