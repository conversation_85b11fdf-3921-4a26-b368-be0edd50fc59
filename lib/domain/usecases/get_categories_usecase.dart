import '../entities/category_entity.dart';
import '../repositories/home_repository_interface.dart';

/// Use case for getting categories
/// Encapsulates the business logic for retrieving categories
class GetCategoriesUseCase {
  final IHomeRepository _homeRepository;

  const GetCategoriesUseCase(this._homeRepository);

  /// Execute the use case to get categories
  ///
  /// [page] - Page number for pagination (0-based)
  /// [pageSize] - Number of items per page
  /// [refresh] - Whether to refresh the data
  /// [query] - Search query filter
  ///
  /// Returns a list of [CategoryEntity]
  Future<List<CategoryEntity>> execute({
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
  }) async {
    // Business logic can be added here
    // For example: validation, caching, filtering, etc.

    // Validate inputs
    if (page < 0) {
      throw ArgumentError('Page number cannot be negative');
    }

    if (pageSize <= 0) {
      throw ArgumentError('Page size must be greater than 0');
    }

    // Call repository to get data
    final categories = await _homeRepository.getCategories(
      page: page,
      pageSize: pageSize,
      refresh: refresh,
      query: query,
    );

    // Apply business rules
    // Return all categories (removed count filter as it was hiding categories)
    return categories;
  }
}
