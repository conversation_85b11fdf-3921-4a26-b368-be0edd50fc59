/// Domain entity representing a product category
/// Pure business object without external dependencies
class CategoryEntity {
  final String id;
  final String name;
  final int count;
  final String? imageUrl;
  final String? icon;
  final CategoryIconColor? iconColor;

  const CategoryEntity({
    required this.id,
    required this.name,
    required this.count,
    this.imageUrl,
    this.icon,
    this.iconColor,
  });

  /// Create a copy with updated values
  CategoryEntity copyWith({
    String? id,
    String? name,
    int? count,
    String? imageUrl,
    String? icon,
    CategoryIconColor? iconColor,
  }) {
    return CategoryEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      count: count ?? this.count,
      imageUrl: imageUrl ?? this.imageUrl,
      icon: icon ?? this.icon,
      iconColor: iconColor ?? this.iconColor,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryEntity &&
        other.id == id &&
        other.name == name &&
        other.count == count &&
        other.imageUrl == imageUrl &&
        other.icon == icon &&
        other.iconColor == iconColor;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        count.hashCode ^
        imageUrl.hashCode ^
        icon.hashCode ^
        iconColor.hashCode;
  }

  @override
  String toString() {
    return 'CategoryEntity(id: $id, name: $name, count: $count, imageUrl: $imageUrl, icon: $icon, iconColor: $iconColor)';
  }
}

/// Domain entity for category icon color
class CategoryIconColor {
  final int red;
  final int green;
  final int blue;
  final double opacity;

  const CategoryIconColor({
    required this.red,
    required this.green,
    required this.blue,
    this.opacity = 1.0,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryIconColor &&
        other.red == red &&
        other.green == green &&
        other.blue == blue &&
        other.opacity == opacity;
  }

  @override
  int get hashCode {
    return red.hashCode ^ green.hashCode ^ blue.hashCode ^ opacity.hashCode;
  }
}
