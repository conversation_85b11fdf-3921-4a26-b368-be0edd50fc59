/// Domain entity representing a home banner
/// Pure business object without external dependencies
class BannerEntity {
  final String id;
  final String imageUrl;
  final String? categoryId;
  final String? categoryName;

  const BannerEntity({
    required this.id,
    required this.imageUrl,
    this.categoryId,
    this.categoryName,
  });

  /// Check if banner is linked to a category
  bool get hasCategory => categoryId != null && categoryId!.isNotEmpty;

  /// Create a copy with updated values
  BannerEntity copyWith({
    String? id,
    String? imageUrl,
    String? categoryId,
    String? categoryName,
  }) {
    return BannerEntity(
      id: id ?? this.id,
      imageUrl: imageUrl ?? this.imageUrl,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BannerEntity &&
        other.id == id &&
        other.imageUrl == imageUrl &&
        other.categoryId == categoryId &&
        other.categoryName == categoryName;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        imageUrl.hashCode ^
        categoryId.hashCode ^
        categoryName.hashCode;
  }

  @override
  String toString() {
    return 'BannerEntity(id: $id, imageUrl: $imageUrl, categoryId: $categoryId, categoryName: $categoryName)';
  }
}
