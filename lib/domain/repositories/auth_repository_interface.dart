import 'package:firebase_auth/firebase_auth.dart';

/// Abstract interface for authentication repository
/// Defines the contract for authentication-related operations
abstract class IAuthRepository {
  /// Verify mobile number for phone authentication
  /// 
  /// [mobileNumber] - The mobile number to verify
  /// [codeSent] - Callback when verification code is sent
  /// [verificationFailed] - Callback when verification fails
  /// [verificationCompleted] - Callback when verification is completed automatically
  Future<void> verifyMobileNumber(
    String mobileNumber, {
    required Function(String, int?) codeSent,
    required Function(Exception) verificationFailed,
    required Function(UserCredential) verificationCompleted,
  });

  /// Validate OTP for phone authentication
  /// 
  /// [verificationId] - The verification ID received from verifyMobileNumber
  /// [otp] - The OTP code entered by user
  /// Returns [UserCredential] on successful verification
  Future<UserCredential> validateOTP(String verificationId, String otp);

  /// Sign in with Google
  /// 
  /// Returns [UserCredential] on successful Google sign-in
  Future<UserCredential> googleSignin();
}
