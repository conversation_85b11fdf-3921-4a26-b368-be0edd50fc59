// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OrderState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)
        orderHistoryLoaded,
    required TResult Function(OrderEntity order) orderDetailsLoaded,
    required TResult Function(String orderId, String message) orderCancelled,
    required TResult Function(String message, String? orderId) error,
    required TResult Function(String filter, String searchQuery) empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult? Function(OrderEntity order)? orderDetailsLoaded,
    TResult? Function(String orderId, String message)? orderCancelled,
    TResult? Function(String message, String? orderId)? error,
    TResult? Function(String filter, String searchQuery)? empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult Function(OrderEntity order)? orderDetailsLoaded,
    TResult Function(String orderId, String message)? orderCancelled,
    TResult Function(String message, String? orderId)? error,
    TResult Function(String filter, String searchQuery)? empty,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInitial value) initial,
    required TResult Function(OrderLoading value) loading,
    required TResult Function(OrderHistoryLoaded value) orderHistoryLoaded,
    required TResult Function(OrderDetailsLoaded value) orderDetailsLoaded,
    required TResult Function(OrderCancelled value) orderCancelled,
    required TResult Function(OrderError value) error,
    required TResult Function(OrderEmpty value) empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInitial value)? initial,
    TResult? Function(OrderLoading value)? loading,
    TResult? Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult? Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult? Function(OrderCancelled value)? orderCancelled,
    TResult? Function(OrderError value)? error,
    TResult? Function(OrderEmpty value)? empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInitial value)? initial,
    TResult Function(OrderLoading value)? loading,
    TResult Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult Function(OrderCancelled value)? orderCancelled,
    TResult Function(OrderError value)? error,
    TResult Function(OrderEmpty value)? empty,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderStateCopyWith<$Res> {
  factory $OrderStateCopyWith(
          OrderState value, $Res Function(OrderState) then) =
      _$OrderStateCopyWithImpl<$Res, OrderState>;
}

/// @nodoc
class _$OrderStateCopyWithImpl<$Res, $Val extends OrderState>
    implements $OrderStateCopyWith<$Res> {
  _$OrderStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$OrderInitialImplCopyWith<$Res> {
  factory _$$OrderInitialImplCopyWith(
          _$OrderInitialImpl value, $Res Function(_$OrderInitialImpl) then) =
      __$$OrderInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OrderInitialImplCopyWithImpl<$Res>
    extends _$OrderStateCopyWithImpl<$Res, _$OrderInitialImpl>
    implements _$$OrderInitialImplCopyWith<$Res> {
  __$$OrderInitialImplCopyWithImpl(
      _$OrderInitialImpl _value, $Res Function(_$OrderInitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OrderInitialImpl implements OrderInitial {
  const _$OrderInitialImpl();

  @override
  String toString() {
    return 'OrderState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$OrderInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)
        orderHistoryLoaded,
    required TResult Function(OrderEntity order) orderDetailsLoaded,
    required TResult Function(String orderId, String message) orderCancelled,
    required TResult Function(String message, String? orderId) error,
    required TResult Function(String filter, String searchQuery) empty,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult? Function(OrderEntity order)? orderDetailsLoaded,
    TResult? Function(String orderId, String message)? orderCancelled,
    TResult? Function(String message, String? orderId)? error,
    TResult? Function(String filter, String searchQuery)? empty,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult Function(OrderEntity order)? orderDetailsLoaded,
    TResult Function(String orderId, String message)? orderCancelled,
    TResult Function(String message, String? orderId)? error,
    TResult Function(String filter, String searchQuery)? empty,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInitial value) initial,
    required TResult Function(OrderLoading value) loading,
    required TResult Function(OrderHistoryLoaded value) orderHistoryLoaded,
    required TResult Function(OrderDetailsLoaded value) orderDetailsLoaded,
    required TResult Function(OrderCancelled value) orderCancelled,
    required TResult Function(OrderError value) error,
    required TResult Function(OrderEmpty value) empty,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInitial value)? initial,
    TResult? Function(OrderLoading value)? loading,
    TResult? Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult? Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult? Function(OrderCancelled value)? orderCancelled,
    TResult? Function(OrderError value)? error,
    TResult? Function(OrderEmpty value)? empty,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInitial value)? initial,
    TResult Function(OrderLoading value)? loading,
    TResult Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult Function(OrderCancelled value)? orderCancelled,
    TResult Function(OrderError value)? error,
    TResult Function(OrderEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class OrderInitial implements OrderState {
  const factory OrderInitial() = _$OrderInitialImpl;
}

/// @nodoc
abstract class _$$OrderLoadingImplCopyWith<$Res> {
  factory _$$OrderLoadingImplCopyWith(
          _$OrderLoadingImpl value, $Res Function(_$OrderLoadingImpl) then) =
      __$$OrderLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OrderLoadingImplCopyWithImpl<$Res>
    extends _$OrderStateCopyWithImpl<$Res, _$OrderLoadingImpl>
    implements _$$OrderLoadingImplCopyWith<$Res> {
  __$$OrderLoadingImplCopyWithImpl(
      _$OrderLoadingImpl _value, $Res Function(_$OrderLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OrderLoadingImpl implements OrderLoading {
  const _$OrderLoadingImpl();

  @override
  String toString() {
    return 'OrderState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$OrderLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)
        orderHistoryLoaded,
    required TResult Function(OrderEntity order) orderDetailsLoaded,
    required TResult Function(String orderId, String message) orderCancelled,
    required TResult Function(String message, String? orderId) error,
    required TResult Function(String filter, String searchQuery) empty,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult? Function(OrderEntity order)? orderDetailsLoaded,
    TResult? Function(String orderId, String message)? orderCancelled,
    TResult? Function(String message, String? orderId)? error,
    TResult? Function(String filter, String searchQuery)? empty,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult Function(OrderEntity order)? orderDetailsLoaded,
    TResult Function(String orderId, String message)? orderCancelled,
    TResult Function(String message, String? orderId)? error,
    TResult Function(String filter, String searchQuery)? empty,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInitial value) initial,
    required TResult Function(OrderLoading value) loading,
    required TResult Function(OrderHistoryLoaded value) orderHistoryLoaded,
    required TResult Function(OrderDetailsLoaded value) orderDetailsLoaded,
    required TResult Function(OrderCancelled value) orderCancelled,
    required TResult Function(OrderError value) error,
    required TResult Function(OrderEmpty value) empty,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInitial value)? initial,
    TResult? Function(OrderLoading value)? loading,
    TResult? Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult? Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult? Function(OrderCancelled value)? orderCancelled,
    TResult? Function(OrderError value)? error,
    TResult? Function(OrderEmpty value)? empty,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInitial value)? initial,
    TResult Function(OrderLoading value)? loading,
    TResult Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult Function(OrderCancelled value)? orderCancelled,
    TResult Function(OrderError value)? error,
    TResult Function(OrderEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class OrderLoading implements OrderState {
  const factory OrderLoading() = _$OrderLoadingImpl;
}

/// @nodoc
abstract class _$$OrderHistoryLoadedImplCopyWith<$Res> {
  factory _$$OrderHistoryLoadedImplCopyWith(_$OrderHistoryLoadedImpl value,
          $Res Function(_$OrderHistoryLoadedImpl) then) =
      __$$OrderHistoryLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<OrderEntity> orders,
      bool isLoadingMore,
      bool hasMoreData,
      int currentPage,
      String currentFilter,
      String searchQuery});
}

/// @nodoc
class __$$OrderHistoryLoadedImplCopyWithImpl<$Res>
    extends _$OrderStateCopyWithImpl<$Res, _$OrderHistoryLoadedImpl>
    implements _$$OrderHistoryLoadedImplCopyWith<$Res> {
  __$$OrderHistoryLoadedImplCopyWithImpl(_$OrderHistoryLoadedImpl _value,
      $Res Function(_$OrderHistoryLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orders = null,
    Object? isLoadingMore = null,
    Object? hasMoreData = null,
    Object? currentPage = null,
    Object? currentFilter = null,
    Object? searchQuery = null,
  }) {
    return _then(_$OrderHistoryLoadedImpl(
      orders: null == orders
          ? _value._orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<OrderEntity>,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMoreData: null == hasMoreData
          ? _value.hasMoreData
          : hasMoreData // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      currentFilter: null == currentFilter
          ? _value.currentFilter
          : currentFilter // ignore: cast_nullable_to_non_nullable
              as String,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OrderHistoryLoadedImpl implements OrderHistoryLoaded {
  const _$OrderHistoryLoadedImpl(
      {required final List<OrderEntity> orders,
      this.isLoadingMore = false,
      this.hasMoreData = true,
      this.currentPage = 0,
      this.currentFilter = '',
      this.searchQuery = ''})
      : _orders = orders;

  final List<OrderEntity> _orders;
  @override
  List<OrderEntity> get orders {
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orders);
  }

  @override
  @JsonKey()
  final bool isLoadingMore;
  @override
  @JsonKey()
  final bool hasMoreData;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final String currentFilter;
  @override
  @JsonKey()
  final String searchQuery;

  @override
  String toString() {
    return 'OrderState.orderHistoryLoaded(orders: $orders, isLoadingMore: $isLoadingMore, hasMoreData: $hasMoreData, currentPage: $currentPage, currentFilter: $currentFilter, searchQuery: $searchQuery)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderHistoryLoadedImpl &&
            const DeepCollectionEquality().equals(other._orders, _orders) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.hasMoreData, hasMoreData) ||
                other.hasMoreData == hasMoreData) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.currentFilter, currentFilter) ||
                other.currentFilter == currentFilter) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_orders),
      isLoadingMore,
      hasMoreData,
      currentPage,
      currentFilter,
      searchQuery);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderHistoryLoadedImplCopyWith<_$OrderHistoryLoadedImpl> get copyWith =>
      __$$OrderHistoryLoadedImplCopyWithImpl<_$OrderHistoryLoadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)
        orderHistoryLoaded,
    required TResult Function(OrderEntity order) orderDetailsLoaded,
    required TResult Function(String orderId, String message) orderCancelled,
    required TResult Function(String message, String? orderId) error,
    required TResult Function(String filter, String searchQuery) empty,
  }) {
    return orderHistoryLoaded(orders, isLoadingMore, hasMoreData, currentPage,
        currentFilter, searchQuery);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult? Function(OrderEntity order)? orderDetailsLoaded,
    TResult? Function(String orderId, String message)? orderCancelled,
    TResult? Function(String message, String? orderId)? error,
    TResult? Function(String filter, String searchQuery)? empty,
  }) {
    return orderHistoryLoaded?.call(orders, isLoadingMore, hasMoreData,
        currentPage, currentFilter, searchQuery);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult Function(OrderEntity order)? orderDetailsLoaded,
    TResult Function(String orderId, String message)? orderCancelled,
    TResult Function(String message, String? orderId)? error,
    TResult Function(String filter, String searchQuery)? empty,
    required TResult orElse(),
  }) {
    if (orderHistoryLoaded != null) {
      return orderHistoryLoaded(orders, isLoadingMore, hasMoreData, currentPage,
          currentFilter, searchQuery);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInitial value) initial,
    required TResult Function(OrderLoading value) loading,
    required TResult Function(OrderHistoryLoaded value) orderHistoryLoaded,
    required TResult Function(OrderDetailsLoaded value) orderDetailsLoaded,
    required TResult Function(OrderCancelled value) orderCancelled,
    required TResult Function(OrderError value) error,
    required TResult Function(OrderEmpty value) empty,
  }) {
    return orderHistoryLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInitial value)? initial,
    TResult? Function(OrderLoading value)? loading,
    TResult? Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult? Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult? Function(OrderCancelled value)? orderCancelled,
    TResult? Function(OrderError value)? error,
    TResult? Function(OrderEmpty value)? empty,
  }) {
    return orderHistoryLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInitial value)? initial,
    TResult Function(OrderLoading value)? loading,
    TResult Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult Function(OrderCancelled value)? orderCancelled,
    TResult Function(OrderError value)? error,
    TResult Function(OrderEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (orderHistoryLoaded != null) {
      return orderHistoryLoaded(this);
    }
    return orElse();
  }
}

abstract class OrderHistoryLoaded implements OrderState {
  const factory OrderHistoryLoaded(
      {required final List<OrderEntity> orders,
      final bool isLoadingMore,
      final bool hasMoreData,
      final int currentPage,
      final String currentFilter,
      final String searchQuery}) = _$OrderHistoryLoadedImpl;

  List<OrderEntity> get orders;
  bool get isLoadingMore;
  bool get hasMoreData;
  int get currentPage;
  String get currentFilter;
  String get searchQuery;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderHistoryLoadedImplCopyWith<_$OrderHistoryLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OrderDetailsLoadedImplCopyWith<$Res> {
  factory _$$OrderDetailsLoadedImplCopyWith(_$OrderDetailsLoadedImpl value,
          $Res Function(_$OrderDetailsLoadedImpl) then) =
      __$$OrderDetailsLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({OrderEntity order});
}

/// @nodoc
class __$$OrderDetailsLoadedImplCopyWithImpl<$Res>
    extends _$OrderStateCopyWithImpl<$Res, _$OrderDetailsLoadedImpl>
    implements _$$OrderDetailsLoadedImplCopyWith<$Res> {
  __$$OrderDetailsLoadedImplCopyWithImpl(_$OrderDetailsLoadedImpl _value,
      $Res Function(_$OrderDetailsLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? order = null,
  }) {
    return _then(_$OrderDetailsLoadedImpl(
      order: null == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as OrderEntity,
    ));
  }
}

/// @nodoc

class _$OrderDetailsLoadedImpl implements OrderDetailsLoaded {
  const _$OrderDetailsLoadedImpl({required this.order});

  @override
  final OrderEntity order;

  @override
  String toString() {
    return 'OrderState.orderDetailsLoaded(order: $order)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderDetailsLoadedImpl &&
            (identical(other.order, order) || other.order == order));
  }

  @override
  int get hashCode => Object.hash(runtimeType, order);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderDetailsLoadedImplCopyWith<_$OrderDetailsLoadedImpl> get copyWith =>
      __$$OrderDetailsLoadedImplCopyWithImpl<_$OrderDetailsLoadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)
        orderHistoryLoaded,
    required TResult Function(OrderEntity order) orderDetailsLoaded,
    required TResult Function(String orderId, String message) orderCancelled,
    required TResult Function(String message, String? orderId) error,
    required TResult Function(String filter, String searchQuery) empty,
  }) {
    return orderDetailsLoaded(order);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult? Function(OrderEntity order)? orderDetailsLoaded,
    TResult? Function(String orderId, String message)? orderCancelled,
    TResult? Function(String message, String? orderId)? error,
    TResult? Function(String filter, String searchQuery)? empty,
  }) {
    return orderDetailsLoaded?.call(order);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult Function(OrderEntity order)? orderDetailsLoaded,
    TResult Function(String orderId, String message)? orderCancelled,
    TResult Function(String message, String? orderId)? error,
    TResult Function(String filter, String searchQuery)? empty,
    required TResult orElse(),
  }) {
    if (orderDetailsLoaded != null) {
      return orderDetailsLoaded(order);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInitial value) initial,
    required TResult Function(OrderLoading value) loading,
    required TResult Function(OrderHistoryLoaded value) orderHistoryLoaded,
    required TResult Function(OrderDetailsLoaded value) orderDetailsLoaded,
    required TResult Function(OrderCancelled value) orderCancelled,
    required TResult Function(OrderError value) error,
    required TResult Function(OrderEmpty value) empty,
  }) {
    return orderDetailsLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInitial value)? initial,
    TResult? Function(OrderLoading value)? loading,
    TResult? Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult? Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult? Function(OrderCancelled value)? orderCancelled,
    TResult? Function(OrderError value)? error,
    TResult? Function(OrderEmpty value)? empty,
  }) {
    return orderDetailsLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInitial value)? initial,
    TResult Function(OrderLoading value)? loading,
    TResult Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult Function(OrderCancelled value)? orderCancelled,
    TResult Function(OrderError value)? error,
    TResult Function(OrderEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (orderDetailsLoaded != null) {
      return orderDetailsLoaded(this);
    }
    return orElse();
  }
}

abstract class OrderDetailsLoaded implements OrderState {
  const factory OrderDetailsLoaded({required final OrderEntity order}) =
      _$OrderDetailsLoadedImpl;

  OrderEntity get order;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderDetailsLoadedImplCopyWith<_$OrderDetailsLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OrderCancelledImplCopyWith<$Res> {
  factory _$$OrderCancelledImplCopyWith(_$OrderCancelledImpl value,
          $Res Function(_$OrderCancelledImpl) then) =
      __$$OrderCancelledImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String orderId, String message});
}

/// @nodoc
class __$$OrderCancelledImplCopyWithImpl<$Res>
    extends _$OrderStateCopyWithImpl<$Res, _$OrderCancelledImpl>
    implements _$$OrderCancelledImplCopyWith<$Res> {
  __$$OrderCancelledImplCopyWithImpl(
      _$OrderCancelledImpl _value, $Res Function(_$OrderCancelledImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = null,
    Object? message = null,
  }) {
    return _then(_$OrderCancelledImpl(
      orderId: null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OrderCancelledImpl implements OrderCancelled {
  const _$OrderCancelledImpl({required this.orderId, required this.message});

  @override
  final String orderId;
  @override
  final String message;

  @override
  String toString() {
    return 'OrderState.orderCancelled(orderId: $orderId, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderCancelledImpl &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, orderId, message);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderCancelledImplCopyWith<_$OrderCancelledImpl> get copyWith =>
      __$$OrderCancelledImplCopyWithImpl<_$OrderCancelledImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)
        orderHistoryLoaded,
    required TResult Function(OrderEntity order) orderDetailsLoaded,
    required TResult Function(String orderId, String message) orderCancelled,
    required TResult Function(String message, String? orderId) error,
    required TResult Function(String filter, String searchQuery) empty,
  }) {
    return orderCancelled(orderId, message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult? Function(OrderEntity order)? orderDetailsLoaded,
    TResult? Function(String orderId, String message)? orderCancelled,
    TResult? Function(String message, String? orderId)? error,
    TResult? Function(String filter, String searchQuery)? empty,
  }) {
    return orderCancelled?.call(orderId, message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult Function(OrderEntity order)? orderDetailsLoaded,
    TResult Function(String orderId, String message)? orderCancelled,
    TResult Function(String message, String? orderId)? error,
    TResult Function(String filter, String searchQuery)? empty,
    required TResult orElse(),
  }) {
    if (orderCancelled != null) {
      return orderCancelled(orderId, message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInitial value) initial,
    required TResult Function(OrderLoading value) loading,
    required TResult Function(OrderHistoryLoaded value) orderHistoryLoaded,
    required TResult Function(OrderDetailsLoaded value) orderDetailsLoaded,
    required TResult Function(OrderCancelled value) orderCancelled,
    required TResult Function(OrderError value) error,
    required TResult Function(OrderEmpty value) empty,
  }) {
    return orderCancelled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInitial value)? initial,
    TResult? Function(OrderLoading value)? loading,
    TResult? Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult? Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult? Function(OrderCancelled value)? orderCancelled,
    TResult? Function(OrderError value)? error,
    TResult? Function(OrderEmpty value)? empty,
  }) {
    return orderCancelled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInitial value)? initial,
    TResult Function(OrderLoading value)? loading,
    TResult Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult Function(OrderCancelled value)? orderCancelled,
    TResult Function(OrderError value)? error,
    TResult Function(OrderEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (orderCancelled != null) {
      return orderCancelled(this);
    }
    return orElse();
  }
}

abstract class OrderCancelled implements OrderState {
  const factory OrderCancelled(
      {required final String orderId,
      required final String message}) = _$OrderCancelledImpl;

  String get orderId;
  String get message;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderCancelledImplCopyWith<_$OrderCancelledImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OrderErrorImplCopyWith<$Res> {
  factory _$$OrderErrorImplCopyWith(
          _$OrderErrorImpl value, $Res Function(_$OrderErrorImpl) then) =
      __$$OrderErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, String? orderId});
}

/// @nodoc
class __$$OrderErrorImplCopyWithImpl<$Res>
    extends _$OrderStateCopyWithImpl<$Res, _$OrderErrorImpl>
    implements _$$OrderErrorImplCopyWith<$Res> {
  __$$OrderErrorImplCopyWithImpl(
      _$OrderErrorImpl _value, $Res Function(_$OrderErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? orderId = freezed,
  }) {
    return _then(_$OrderErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$OrderErrorImpl implements OrderError {
  const _$OrderErrorImpl({required this.message, this.orderId});

  @override
  final String message;
  @override
  final String? orderId;

  @override
  String toString() {
    return 'OrderState.error(message: $message, orderId: $orderId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.orderId, orderId) || other.orderId == orderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, orderId);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderErrorImplCopyWith<_$OrderErrorImpl> get copyWith =>
      __$$OrderErrorImplCopyWithImpl<_$OrderErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)
        orderHistoryLoaded,
    required TResult Function(OrderEntity order) orderDetailsLoaded,
    required TResult Function(String orderId, String message) orderCancelled,
    required TResult Function(String message, String? orderId) error,
    required TResult Function(String filter, String searchQuery) empty,
  }) {
    return error(message, orderId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult? Function(OrderEntity order)? orderDetailsLoaded,
    TResult? Function(String orderId, String message)? orderCancelled,
    TResult? Function(String message, String? orderId)? error,
    TResult? Function(String filter, String searchQuery)? empty,
  }) {
    return error?.call(message, orderId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult Function(OrderEntity order)? orderDetailsLoaded,
    TResult Function(String orderId, String message)? orderCancelled,
    TResult Function(String message, String? orderId)? error,
    TResult Function(String filter, String searchQuery)? empty,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message, orderId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInitial value) initial,
    required TResult Function(OrderLoading value) loading,
    required TResult Function(OrderHistoryLoaded value) orderHistoryLoaded,
    required TResult Function(OrderDetailsLoaded value) orderDetailsLoaded,
    required TResult Function(OrderCancelled value) orderCancelled,
    required TResult Function(OrderError value) error,
    required TResult Function(OrderEmpty value) empty,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInitial value)? initial,
    TResult? Function(OrderLoading value)? loading,
    TResult? Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult? Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult? Function(OrderCancelled value)? orderCancelled,
    TResult? Function(OrderError value)? error,
    TResult? Function(OrderEmpty value)? empty,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInitial value)? initial,
    TResult Function(OrderLoading value)? loading,
    TResult Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult Function(OrderCancelled value)? orderCancelled,
    TResult Function(OrderError value)? error,
    TResult Function(OrderEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class OrderError implements OrderState {
  const factory OrderError(
      {required final String message,
      final String? orderId}) = _$OrderErrorImpl;

  String get message;
  String? get orderId;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderErrorImplCopyWith<_$OrderErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OrderEmptyImplCopyWith<$Res> {
  factory _$$OrderEmptyImplCopyWith(
          _$OrderEmptyImpl value, $Res Function(_$OrderEmptyImpl) then) =
      __$$OrderEmptyImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String filter, String searchQuery});
}

/// @nodoc
class __$$OrderEmptyImplCopyWithImpl<$Res>
    extends _$OrderStateCopyWithImpl<$Res, _$OrderEmptyImpl>
    implements _$$OrderEmptyImplCopyWith<$Res> {
  __$$OrderEmptyImplCopyWithImpl(
      _$OrderEmptyImpl _value, $Res Function(_$OrderEmptyImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filter = null,
    Object? searchQuery = null,
  }) {
    return _then(_$OrderEmptyImpl(
      filter: null == filter
          ? _value.filter
          : filter // ignore: cast_nullable_to_non_nullable
              as String,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OrderEmptyImpl implements OrderEmpty {
  const _$OrderEmptyImpl({this.filter = '', this.searchQuery = ''});

  @override
  @JsonKey()
  final String filter;
  @override
  @JsonKey()
  final String searchQuery;

  @override
  String toString() {
    return 'OrderState.empty(filter: $filter, searchQuery: $searchQuery)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderEmptyImpl &&
            (identical(other.filter, filter) || other.filter == filter) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery));
  }

  @override
  int get hashCode => Object.hash(runtimeType, filter, searchQuery);

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderEmptyImplCopyWith<_$OrderEmptyImpl> get copyWith =>
      __$$OrderEmptyImplCopyWithImpl<_$OrderEmptyImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)
        orderHistoryLoaded,
    required TResult Function(OrderEntity order) orderDetailsLoaded,
    required TResult Function(String orderId, String message) orderCancelled,
    required TResult Function(String message, String? orderId) error,
    required TResult Function(String filter, String searchQuery) empty,
  }) {
    return empty(filter, searchQuery);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult? Function(OrderEntity order)? orderDetailsLoaded,
    TResult? Function(String orderId, String message)? orderCancelled,
    TResult? Function(String message, String? orderId)? error,
    TResult? Function(String filter, String searchQuery)? empty,
  }) {
    return empty?.call(filter, searchQuery);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            List<OrderEntity> orders,
            bool isLoadingMore,
            bool hasMoreData,
            int currentPage,
            String currentFilter,
            String searchQuery)?
        orderHistoryLoaded,
    TResult Function(OrderEntity order)? orderDetailsLoaded,
    TResult Function(String orderId, String message)? orderCancelled,
    TResult Function(String message, String? orderId)? error,
    TResult Function(String filter, String searchQuery)? empty,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(filter, searchQuery);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderInitial value) initial,
    required TResult Function(OrderLoading value) loading,
    required TResult Function(OrderHistoryLoaded value) orderHistoryLoaded,
    required TResult Function(OrderDetailsLoaded value) orderDetailsLoaded,
    required TResult Function(OrderCancelled value) orderCancelled,
    required TResult Function(OrderError value) error,
    required TResult Function(OrderEmpty value) empty,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderInitial value)? initial,
    TResult? Function(OrderLoading value)? loading,
    TResult? Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult? Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult? Function(OrderCancelled value)? orderCancelled,
    TResult? Function(OrderError value)? error,
    TResult? Function(OrderEmpty value)? empty,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderInitial value)? initial,
    TResult Function(OrderLoading value)? loading,
    TResult Function(OrderHistoryLoaded value)? orderHistoryLoaded,
    TResult Function(OrderDetailsLoaded value)? orderDetailsLoaded,
    TResult Function(OrderCancelled value)? orderCancelled,
    TResult Function(OrderError value)? error,
    TResult Function(OrderEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class OrderEmpty implements OrderState {
  const factory OrderEmpty({final String filter, final String searchQuery}) =
      _$OrderEmptyImpl;

  String get filter;
  String get searchQuery;

  /// Create a copy of OrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderEmptyImplCopyWith<_$OrderEmptyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
