export 'location_event.dart';
export 'location_state.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../../../../data/models/adress_model.dart';
import '../../services/adress_services.dart';
import '../../services/locaion_services.dart';
import 'location_event.dart';
import 'location_state.dart';

class LocationBloc extends Bloc<LocationEvent, LocationState> {
  final AddressService addressService;
  final LocationService locationService;

  LocationBloc({
    required this.addressService,
    required this.locationService,
  }) : super(const LocationState.initial()) {
    on<LocationEvent>(
      (event, emit) => event.map(
        started: (_) => _onStarted(emit),
        refreshLocation: (e) => _onRefreshLocation(emit),
      ),
    );
  }

  Future<void> _onStarted(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      final defaultAddress = await addressService.getDefaultAddress();
      if (defaultAddress != null) {
        emit(LocationState.loaded(defaultAddress));
      } else {
        add(const LocationEvent.refreshLocation());
      }
    } catch (e) {
      emit(LocationState.error('Failed to load default location.'));
    }
  }

  Future<void> _onRefreshLocation(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());

    try {
      final savedAddresses = await addressService.getAllAddresses();
      final currentPosition = await locationService.getCurrentPosition();

      if (currentPosition != null) {
        if (savedAddresses.isEmpty) {
          final placemarks = await locationService.getAddressFromCoordinates(
            currentPosition.latitude,
            currentPosition.longitude,
          );
          if (placemarks != null && placemarks.isNotEmpty) {
            final address =
                _createTempAddress(currentPosition, placemarks.first);
            emit(LocationState.loaded(address));
            return;
          }
        } else {
          AddressModel? closest;
          double minDist = double.infinity;

          for (final addr in savedAddresses) {
            final dist = Geolocator.distanceBetween(
              currentPosition.latitude,
              currentPosition.longitude,
              (addr.latitude ?? 0.0).toDouble(),
              (addr.longitude ?? 0.0).toDouble(),
            );
            if (dist < minDist) {
              minDist = dist;
              closest = addr;
            }
          }

          if (closest != null) {
            if (!(closest.isDefault ?? false)) {
              await addressService.setDefaultAddress(closest.id ?? '');
            }
            emit(LocationState.loaded(closest));
            return;
          }
        }
      }

      final fallback = await addressService.getDefaultAddress();
      if (fallback != null) {
        emit(LocationState.loaded(fallback));
      } else {
        emit(const LocationState.error('No address available.'));
      }
    } catch (e) {
      emit(LocationState.error('Error while refreshing location.'));
    }
  }

  AddressModel _createTempAddress(Position pos, Placemark pm) {
    final fullAddress = [
      pm.street,
      pm.subLocality,
      pm.locality,
      pm.administrativeArea,
      pm.postalCode
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    final addressLine1 = [
      pm.street,
      pm.subLocality,
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    return AddressModel(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      fullAddress: fullAddress,
      addressLine1: addressLine1,
      city: pm.locality ?? '',
      state: pm.administrativeArea ?? '',
      pincode: pm.postalCode ?? '',
      latitude: pos.latitude,
      longitude: pos.longitude,
      addressType: 'current',
      isDefault: true,
    );
  }

  /// Reset location state - useful for user logout scenarios
  void resetLocation() {
    add(const LocationEvent.started());
  }

  @override
  Future<void> close() {
    // Clean up any location listeners or resources
    // LocationService and AddressService cleanup handled by DI container
    return super.close();
  }
}
