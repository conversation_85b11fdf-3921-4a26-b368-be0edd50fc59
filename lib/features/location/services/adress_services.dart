import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/models/adress_model.dart';

class AddressService {
  // Singleton instance
  static final AddressService _instance = AddressService._internal();
  factory AddressService() => _instance;
  AddressService._internal();

  // Keys for SharedPreferences
  static const String _addressesKey = 'user_addresses';
  static const String _defaultAddressKey = 'default_address_id';

  // In-memory cache of addresses
  List<AddressModel> _addresses = [];
  String? _defaultAddressId;

  // Get all addresses
  Future<List<AddressModel>> getAllAddresses() async {
    if (_addresses.isNotEmpty) {
      return _addresses;
    }

    final prefs = await SharedPreferences.getInstance();
    final addressesJson = prefs.getStringList(_addressesKey) ?? [];

    _addresses = addressesJson
        .map((json) => AddressModel.fromJson(jsonDecode(json)))
        .toList();

    return _addresses;
  }

  // Get default address
  Future<AddressModel?> getDefaultAddress() async {
    final addresses = await getAllAddresses();

    if (addresses.isEmpty) {
      return null;
    }

    // Check if we have a saved default address ID
    if (_defaultAddressId == null) {
      final prefs = await SharedPreferences.getInstance();
      _defaultAddressId = prefs.getString(_defaultAddressKey);
    }

    // If we have a default address ID, find it in the list
    if (_defaultAddressId != null) {
      final defaultAddress = addresses.firstWhere(
        (address) => address.id == _defaultAddressId,
        orElse: () => addresses.firstWhere(
          (address) => (address.isDefault ?? false),
          orElse: () => addresses.first,
        ),
      );
      return defaultAddress;
    }

    // Otherwise, find the first address marked as default
    final defaultAddress = addresses.firstWhere(
      (address) => (address.isDefault ?? false),
      orElse: () => addresses.first,
    );

    return defaultAddress;
  }

  // Add or update address
  Future<void> saveAddress(AddressModel address) async {
    final addresses = await getAllAddresses();

    // Check if address already exists
    final index = addresses.indexWhere((a) => a.id == address.id);

    if (index >= 0) {
      // Update existing address
      addresses[index] = address;
    } else {
      // Add new address
      addresses.add(address);
    }

    // If this is the default address, update all others to not be default
    if (address.isDefault ?? false) {
      for (int i = 0; i < addresses.length; i++) {
        if (addresses[i].id != address.id &&
            (addresses[i].isDefault ?? false)) {
          addresses[i] = addresses[i].copyWith(isDefault: false);
        }
      }

      // Update default address ID
      _defaultAddressId = address.id;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_defaultAddressKey, address.id ?? '');
    }

    // Update in-memory cache
    _addresses = addresses;

    // Save to SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final addressesJson =
        addresses.map((address) => jsonEncode(address.toJson())).toList();

    await prefs.setStringList(_addressesKey, addressesJson);
  }

  // Delete address
  Future<void> deleteAddress(String addressId) async {
    final addresses = await getAllAddresses();

    // Remove address
    addresses.removeWhere((address) => address.id == addressId);

    // If we deleted the default address, update the default
    if (_defaultAddressId == addressId && addresses.isNotEmpty) {
      final newDefault = addresses.first;
      _defaultAddressId = newDefault.id;

      // Update the new default address
      final updatedDefault = newDefault.copyWith(isDefault: true);
      final index = addresses.indexWhere((a) => a.id == updatedDefault.id);
      addresses[index] = updatedDefault;

      // Save the new default address ID
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_defaultAddressKey, updatedDefault.id ?? '');
    }

    // Update in-memory cache
    _addresses = addresses;

    // Save to SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final addressesJson =
        addresses.map((address) => jsonEncode(address.toJson())).toList();

    await prefs.setStringList(_addressesKey, addressesJson);
  }

  // Set default address
  Future<void> setDefaultAddress(String addressId) async {
    final addresses = await getAllAddresses();

    // Find the address
    final index = addresses.indexWhere((address) => address.id == addressId);

    if (index < 0) {
      return;
    }

    // Update all addresses
    for (int i = 0; i < addresses.length; i++) {
      if (i == index) {
        addresses[i] = addresses[i].copyWith(isDefault: true);
      } else if (addresses[i].isDefault ?? false) {
        addresses[i] = addresses[i].copyWith(isDefault: false);
      }
    }

    // Update default address ID
    _defaultAddressId = addressId;

    // Update in-memory cache
    _addresses = addresses;

    // Save to SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final addressesJson =
        addresses.map((address) => jsonEncode(address.toJson())).toList();

    await prefs.setStringList(_addressesKey, addressesJson);
    await prefs.setString(_defaultAddressKey, addressId);
  }

  // Check if location permission is granted
  Future<bool> checkLocationPermission() async {
    final status = await Permission.location.status;
    return status.isGranted;
  }

  // Request location permission
  Future<bool> requestLocationPermission() async {
    final status = await Permission.location.request();
    return status.isGranted;
  }

  // Get current position
  Future<Position?> getCurrentPosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled.
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Location services are not enabled
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // Permissions are denied
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle appropriately.
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    try {
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
    } catch (e) {
      debugPrint('Error getting current position: $e');
      return null;
    }
  }

  // Get address from coordinates
  Future<List<Placemark>?> getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      return await placemarkFromCoordinates(latitude, longitude);
    } catch (e) {
      debugPrint('Error getting address from coordinates: $e');
      return null;
    }
  }

  // Convert Placemark to AddressModel
  AddressModel placemarkToAddressModel(Placemark placemark, Position position,
      {String addressType = 'home', bool isDefault = false}) {
    final id = DateTime.now().millisecondsSinceEpoch.toString();

    final street = placemark.street ?? '';
    final subLocality = placemark.subLocality ?? '';
    final locality = placemark.locality ?? '';
    final administrativeArea = placemark.administrativeArea ?? '';
    final postalCode = placemark.postalCode ?? '';

    // Create address line 1 from street and subLocality
    final addressLine1 =
        [street, subLocality].where((e) => e.isNotEmpty).join(', ');

    // Create full address
    final fullAddress = [
      street,
      subLocality,
      locality,
      administrativeArea,
      postalCode,
    ].where((e) => e.isNotEmpty).join(', ');

    return AddressModel(
      id: id,
      fullAddress: fullAddress,
      addressLine1: addressLine1,
      city: locality,
      state: administrativeArea,
      pincode: postalCode,
      latitude: position.latitude,
      longitude: position.longitude,
      addressType: addressType,
      isDefault: isDefault,
    );
  }

  // Search for addresses based on query
  Future<List<Location>?> searchAddresses(String query) async {
    try {
      return await locationFromAddress(query);
    } catch (e) {
      debugPrint('Error searching addresses: $e');
      return null;
    }
  }

  // Get placemark from location
  Future<List<Placemark>?> getPlacemarkFromLocation(Location location) async {
    try {
      return await placemarkFromCoordinates(
          location.latitude, location.longitude);
    } catch (e) {
      debugPrint('Error getting placemark from location: $e');
      return null;
    }
  }

  // Auto-detect current location and set as default if no addresses exist
  Future<AddressModel?> autoDetectLocationAndSetDefault() async {
    try {
      // Check if we already have addresses
      final addresses = await getAllAddresses();
      if (addresses.isNotEmpty) {
        return await getDefaultAddress();
      }

      // Get current position
      final position = await getCurrentPosition();
      if (position == null) {
        return null;
      }

      // Get address from coordinates
      final placemarks = await getAddressFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks == null || placemarks.isEmpty) {
        return null;
      }

      // Create address model
      final address = placemarkToAddressModel(
        placemarks.first,
        position,
        isDefault: true,
      );

      // Save address
      await saveAddress(address);

      return address;
    } catch (e) {
      debugPrint('Error auto-detecting location: $e');
      return null;
    }
  }

  // Find nearest address to current location
  Future<AddressModel?> findNearestAddress() async {
    try {
      // Get current position
      final position = await getCurrentPosition();
      if (position == null) {
        return await getDefaultAddress();
      }

      // Get all addresses
      final addresses = await getAllAddresses();
      if (addresses.isEmpty) {
        return null;
      }

      // Calculate distance to each address
      AddressModel? nearestAddress;
      double nearestDistance = double.infinity;

      for (final address in addresses) {
        final distance = Geolocator.distanceBetween(
          position.latitude,
          position.longitude,
          (address.latitude ?? 0.0).toDouble(),
          (address.longitude ?? 0.0).toDouble(),
        );

        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearestAddress = address;
        }
      }

      return nearestAddress ?? addresses.first;
    } catch (e) {
      debugPrint('Error finding nearest address: $e');
      return await getDefaultAddress();
    }
  }
}
