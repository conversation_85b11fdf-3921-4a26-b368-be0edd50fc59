import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../domain/entities/category_entity.dart';
import '../../../domain/usecases/get_categories_usecase.dart';

part 'categories_event.dart';
part 'categories_state.dart';
part 'categories_bloc.freezed.dart';

class CategoriesBloc extends Bloc<CategoriesEvent, CategoriesState> {
  final GetCategoriesUseCase _getCategoriesUseCase;
  CategoriesBloc(this._getCategoriesUseCase)
      : super(CategoriesState.initial()) {
    on<CategoriesEvent>(
      (event, emit) => event.map(
        fetchCategories: (_) => _onFetchCategories(emit),
        updateLoadedList: (e) => _onUpdateCategoryList(e.categories, emit),
      ),
    );
  }

  Future<void> _onFetchCategories(Emitter<CategoriesState> emit) async {
    try {
      // Start all futures without awaiting
      final categoriesFuture = _getCategoriesUseCase.execute();

      // Keep current progressive data
      List<CategoryEntity>? categories;

      categoriesFuture.then((value) {
        categories = value;
        add(CategoriesEvent.updateLoadedList(categories: categories ?? []));
      });
    } catch (e) {
      emit(CategoriesState.error(message: e.toString()));
    }
  }

  void _onUpdateCategoryList(
      List<CategoryEntity>? categories, Emitter<CategoriesState> emit) {
    state.maybeMap(
      loaded: (value) {
        emit(value.copyWith(
          categories: categories ?? value.categories,
        ));
      },
      orElse: () {
        emit(CategoriesState.loaded(
          categories: categories,
        ));
      },
    );
  }
}
