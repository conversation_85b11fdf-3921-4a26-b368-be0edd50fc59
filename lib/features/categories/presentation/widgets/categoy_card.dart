import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/utils/color_utils.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../routes/app_router.dart';

class CategoryOfferCard extends StatelessWidget {
  final CategoryEntity? category;
  final CategoryEntity? subCategory;

  const CategoryOfferCard({
    super.key,
    required this.category,
    this.subCategory,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        Map<String, dynamic> extras = {
          'categoryId': category?.id,
          'categoryName': category?.name,
        };
        if (subCategory != null) {
          extras['subCategoryId'] = subCategory?.id;
          extras['subCategoryName'] = subCategory?.name;
        }

        context.push(RouteNames.products, extra: extras);
      },
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Container(
            width: 150, // Adjust width as needed
            margin: EdgeInsets.only(top: 3),
            decoration: BoxDecoration(
              color: Color(0xFFFDF4E9),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 20),
                Spacer(),
                CustomImage(
                  imageUrl: category?.imageUrl,
                  height: 60,
                ),
                const SizedBox(height: 10),
                Spacer(),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CustomText(
                          category?.name ?? 'NA',
                          fontWeight: FontWeight.w700,
                          fontSize: 12,
                          maxLines: 2,
                        ),
                      ),
                      SizedBox(width: 5),
                      Image.asset(
                        'assets/icons/right_arrow.png',
                        width: 10,
                      )
                    ],
                  ),
                ),
                const SizedBox(height: 12),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
                color: const Color(0xFFFFDA63), // Match the banner color
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(5),
                    bottomRight: Radius.circular(5)),
                border: Border.all(
                  color: AppColors.primaryAverage,
                  width: 1.5,
                )),
            child: FittedBox(
              child: CustomText(
                'Buy 1 Get 1',
                fontWeight: FontWeight.w800,
                fontSize: 10,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CategoryCard extends StatelessWidget {
  const CategoryCard({
    super.key,
    this.category,
    this.width,
    this.height,
    this.radius,
    this.imagePadding,
    this.subCategory,
    this.subCategoryTheme = false,
    this.fontSize = 12,
    this.onTap,
  });
  final CategoryEntity? category;
  final CategoryEntity? subCategory;

  final double? width;
  final double? height;
  final double? radius;
  final EdgeInsetsGeometry? imagePadding;
  final bool subCategoryTheme;
  final double fontSize;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    CategoryEntity? currentCategory = subCategory ?? category;
    return GestureDetector(
        onTap: onTap ??
            () {
              HapticFeedback.lightImpact();
              Map<String, dynamic> extras = {
                'categoryId': category?.id,
                'categoryName': category?.name,
              };
              if (subCategory != null) {
                extras['subCategoryId'] = subCategory?.id;
                extras['subCategoryName'] = subCategory?.name;
              }

              context.push(RouteNames.products, extra: extras);
            },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: width ?? 100,
              height: subCategoryTheme ? null : (height ?? 100),
              decoration: BoxDecoration(
                  color: ColorUtils.generateConsistentColor(
                      currentCategory?.id ?? '',
                      baseOpacity: 0.2),
                  borderRadius: BorderRadius.circular(radius ?? 20)),
              child: ClipRRect(
                  borderRadius: BorderRadiusGeometry.circular(radius ?? 20),
                  child: Padding(
                    padding: imagePadding ?? EdgeInsets.zero,
                    child: Column(
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(
                              maxHeight: (height ?? 100) -
                                  (imagePadding?.vertical ?? 0),
                              minWidth: (width ?? 100) -
                                  (imagePadding?.horizontal ?? 0)),
                          child: CustomImage(
                            imageUrl: currentCategory?.imageUrl,
                            height: ((height != null) && subCategoryTheme)
                                ? (height! - 40)
                                : null,
                          ),
                        ),
                        Visibility(
                          visible: subCategoryTheme,
                          child: ConstrainedBox(
                            constraints: BoxConstraints(minHeight: 33),
                            child: Padding(
                              padding: const EdgeInsets.only(top: 5),
                              child: Center(
                                child: CustomText(
                                  currentCategory?.name ?? '',
                                  fontSize: fontSize,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF24292E),
                                  maxLines: 2,
                                  textAlign: TextAlign.center,
                                  textHeight: 1.2,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
            ),
            Visibility(
              visible: !subCategoryTheme,
              child: Padding(
                padding: const EdgeInsets.only(top: 5),
                child: CustomText(
                  currentCategory?.name ?? '',
                  fontSize: fontSize,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF24292E),
                  maxLines: 2,
                  textAlign: TextAlign.center,
                  textHeight: 1.2,
                ),
              ),
            ),
          ],
        ));
  }
}
