import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class CategorySkeletonLoader extends StatelessWidget {
  final bool useGridView;
  final bool showAsRow;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;

  const CategorySkeletonLoader({
    super.key,
    this.useGridView = false,
    this.showAsRow = false,
    this.gridCrossAxisCount = 4,
    this.gridChildAspectRatio = 0.75,
  });

  static const int _itemCount = 6;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: showAsRow
          ? _buildRowSkeleton(context)
          : useGridView
              ? _buildCustomGridSkeleton(context)
              : _buildDefaultGridSkeleton(context),
    );
  }

  Widget _buildDefaultGridSkeleton(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 12),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        childAspectRatio: 0.75,
        crossAxisSpacing: 8,
        mainAxisSpacing: 12,
      ),
      itemCount: _itemCount,
      itemBuilder: (context, index) => _buildBox(width: 55, height: 70),
    );
  }

  Widget _buildCustomGridSkeleton(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = (screenWidth - 44) / gridCrossAxisCount;
    final cardHeight = cardWidth / gridChildAspectRatio;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: gridCrossAxisCount,
        childAspectRatio: gridChildAspectRatio,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: _itemCount,
      itemBuilder: (context, index) =>
          _buildBox(width: cardWidth, height: cardHeight),
    );
  }

  Widget _buildRowSkeleton(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = screenWidth - 32;

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _itemCount,
      itemBuilder: (context, index) => Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: _buildBox(width: cardWidth, height: 70),
      ),
    );
  }

  Widget _buildBox({required double width, required double height}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }
}