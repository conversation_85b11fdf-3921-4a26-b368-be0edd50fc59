import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/utils/color_utils.dart';
import '../../bloc/categories_bloc.dart';
import '../widgets/subcategories_section.dart';

class CategoriesScreen extends StatelessWidget {
  const CategoriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        elevation: 0.5,
        title: const Text(
          'Categories',
          style: TextStyle(
            color: Colors.black87,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => context.pop(),
        ),
      ),
      body: BlocBuilder<CategoriesBloc, CategoriesState>(
        builder: (context, state) {
          return state.map(initial: (_) {
            return const Center(child: CircularProgressIndicator());
          }, loaded: (value) {
            return RefreshIndicator(
              onRefresh: () async {
                context
                    .read<CategoriesBloc>()
                    .add(CategoriesEvent.fetchCategories());
              },
              child: (value.categories?.isEmpty ?? false)
                  ? const Center(child: Text('No categories found'))
                  : ListView.builder(
                      padding: const EdgeInsets.all(12),
                      itemCount: value.categories?.length,
                      itemBuilder: (context, index) {
                        CategoryEntity? category = value.categories?[index];
                        return CategorySectionWidget(
                          category: category,
                        );
                      },
                    ),
            );
          }, error: (value) {
            return Center(
              child: CustomText(value.message),
            );
          });
        },
      ),
    );
  }
}

class CategorySectionWidget extends StatelessWidget {
  const CategorySectionWidget({
    super.key,
    this.category,
  });
  final CategoryEntity? category;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: ColorUtils.generateConsistentColor(category?.id ?? '',
                baseOpacity: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: SubCategoriesSection(
              preloadData: true,
              parentCategory: category,
              onSeeAllTap: () {
                context.push(RouteNames.products, extra: {
                  'categoryId': category?.id,
                  'categoryName': category?.name,
                });
              },
            ),
          ),
        ),
        const SizedBox(height: 12),
      ],
    );
  }
}
