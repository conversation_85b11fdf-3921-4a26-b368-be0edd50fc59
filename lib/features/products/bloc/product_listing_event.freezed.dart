// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_listing_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ProductListingEvent {
  CategoryModel? get subCategory => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CategoryModel? category, CategoryModel? subCategory)
        initial,
    required TResult Function(CategoryModel? subCategory) selectSubcategory,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CategoryModel? category, CategoryModel? subCategory)?
        initial,
    TResult? Function(CategoryModel? subCategory)? selectSubcategory,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CategoryModel? category, CategoryModel? subCategory)?
        initial,
    TResult Function(CategoryModel? subCategory)? selectSubcategory,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SelectSubcategory value) selectSubcategory,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SelectSubcategory value)? selectSubcategory,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SelectSubcategory value)? selectSubcategory,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductListingEventCopyWith<ProductListingEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductListingEventCopyWith<$Res> {
  factory $ProductListingEventCopyWith(
          ProductListingEvent value, $Res Function(ProductListingEvent) then) =
      _$ProductListingEventCopyWithImpl<$Res, ProductListingEvent>;
  @useResult
  $Res call({CategoryModel? subCategory});
}

/// @nodoc
class _$ProductListingEventCopyWithImpl<$Res, $Val extends ProductListingEvent>
    implements $ProductListingEventCopyWith<$Res> {
  _$ProductListingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subCategory = freezed,
  }) {
    return _then(_value.copyWith(
      subCategory: freezed == subCategory
          ? _value.subCategory
          : subCategory // ignore: cast_nullable_to_non_nullable
              as CategoryModel?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res>
    implements $ProductListingEventCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({CategoryModel? category, CategoryModel? subCategory});
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$ProductListingEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? category = freezed,
    Object? subCategory = freezed,
  }) {
    return _then(_$InitialImpl(
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as CategoryModel?,
      subCategory: freezed == subCategory
          ? _value.subCategory
          : subCategory // ignore: cast_nullable_to_non_nullable
              as CategoryModel?,
    ));
  }
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl({this.category, this.subCategory});

  @override
  final CategoryModel? category;
  @override
  final CategoryModel? subCategory;

  @override
  String toString() {
    return 'ProductListingEvent.initial(category: $category, subCategory: $subCategory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InitialImpl &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.subCategory, subCategory) ||
                other.subCategory == subCategory));
  }

  @override
  int get hashCode => Object.hash(runtimeType, category, subCategory);

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InitialImplCopyWith<_$InitialImpl> get copyWith =>
      __$$InitialImplCopyWithImpl<_$InitialImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CategoryModel? category, CategoryModel? subCategory)
        initial,
    required TResult Function(CategoryModel? subCategory) selectSubcategory,
  }) {
    return initial(category, subCategory);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CategoryModel? category, CategoryModel? subCategory)?
        initial,
    TResult? Function(CategoryModel? subCategory)? selectSubcategory,
  }) {
    return initial?.call(category, subCategory);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CategoryModel? category, CategoryModel? subCategory)?
        initial,
    TResult Function(CategoryModel? subCategory)? selectSubcategory,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(category, subCategory);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SelectSubcategory value) selectSubcategory,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SelectSubcategory value)? selectSubcategory,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SelectSubcategory value)? selectSubcategory,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements ProductListingEvent {
  const factory _Initial(
      {final CategoryModel? category,
      final CategoryModel? subCategory}) = _$InitialImpl;

  CategoryModel? get category;
  @override
  CategoryModel? get subCategory;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InitialImplCopyWith<_$InitialImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectSubcategoryImplCopyWith<$Res>
    implements $ProductListingEventCopyWith<$Res> {
  factory _$$SelectSubcategoryImplCopyWith(_$SelectSubcategoryImpl value,
          $Res Function(_$SelectSubcategoryImpl) then) =
      __$$SelectSubcategoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({CategoryModel? subCategory});
}

/// @nodoc
class __$$SelectSubcategoryImplCopyWithImpl<$Res>
    extends _$ProductListingEventCopyWithImpl<$Res, _$SelectSubcategoryImpl>
    implements _$$SelectSubcategoryImplCopyWith<$Res> {
  __$$SelectSubcategoryImplCopyWithImpl(_$SelectSubcategoryImpl _value,
      $Res Function(_$SelectSubcategoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subCategory = freezed,
  }) {
    return _then(_$SelectSubcategoryImpl(
      subCategory: freezed == subCategory
          ? _value.subCategory
          : subCategory // ignore: cast_nullable_to_non_nullable
              as CategoryModel?,
    ));
  }
}

/// @nodoc

class _$SelectSubcategoryImpl implements _SelectSubcategory {
  const _$SelectSubcategoryImpl({this.subCategory});

  @override
  final CategoryModel? subCategory;

  @override
  String toString() {
    return 'ProductListingEvent.selectSubcategory(subCategory: $subCategory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectSubcategoryImpl &&
            (identical(other.subCategory, subCategory) ||
                other.subCategory == subCategory));
  }

  @override
  int get hashCode => Object.hash(runtimeType, subCategory);

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectSubcategoryImplCopyWith<_$SelectSubcategoryImpl> get copyWith =>
      __$$SelectSubcategoryImplCopyWithImpl<_$SelectSubcategoryImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CategoryModel? category, CategoryModel? subCategory)
        initial,
    required TResult Function(CategoryModel? subCategory) selectSubcategory,
  }) {
    return selectSubcategory(subCategory);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CategoryModel? category, CategoryModel? subCategory)?
        initial,
    TResult? Function(CategoryModel? subCategory)? selectSubcategory,
  }) {
    return selectSubcategory?.call(subCategory);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CategoryModel? category, CategoryModel? subCategory)?
        initial,
    TResult Function(CategoryModel? subCategory)? selectSubcategory,
    required TResult orElse(),
  }) {
    if (selectSubcategory != null) {
      return selectSubcategory(subCategory);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_SelectSubcategory value) selectSubcategory,
  }) {
    return selectSubcategory(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_SelectSubcategory value)? selectSubcategory,
  }) {
    return selectSubcategory?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_SelectSubcategory value)? selectSubcategory,
    required TResult orElse(),
  }) {
    if (selectSubcategory != null) {
      return selectSubcategory(this);
    }
    return orElse();
  }
}

abstract class _SelectSubcategory implements ProductListingEvent {
  const factory _SelectSubcategory({final CategoryModel? subCategory}) =
      _$SelectSubcategoryImpl;

  @override
  CategoryModel? get subCategory;

  /// Create a copy of ProductListingEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelectSubcategoryImplCopyWith<_$SelectSubcategoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
