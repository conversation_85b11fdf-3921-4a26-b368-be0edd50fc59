import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/data/models/category_model.dart';

import 'product_listing_event.dart';
import 'product_listing_state.dart';

class ProductListingBloc
    extends Bloc<ProductListingEvent, ProductListingState> {
  ProductListingBloc() : super(const ProductListingState.loading()) {
    // Register event handlers using .map for Freezed events
    on<ProductListingEvent>((event, emit) async {
      await event.when(
        initial: (
          category,
          subCategory,
        ) async {
          await _onInitialEvent(
            category,
            subCategory,
            emit,
          );
        },
        selectSubcategory: (subcategory) {
          _onSelectSubcategoryEvent(subcategory, emit);
        },
      );
    });
  }

  // Event handler for initial setup
  Future<void> _onInitialEvent(
    CategoryModel? category,
    CategoryModel? subCategory,
    Emitter<ProductListingState> emit,
  ) async {
    emit(const ProductListingState.loading());

    if (subCategory == null && (category != null)) {
      subCategory = CategoryModel(id: 'All', name: 'All');
    }

    emit(ProductListingState.loaded(
        category: category, subCategory: subCategory));
  }

  // Event handler for selecting a subcategory
  void _onSelectSubcategoryEvent(
    CategoryModel? subCategory,
    Emitter<ProductListingState> emit,
  ) {
    state.whenOrNull(
      loaded: (category, selectedSubCategory) {
        emit(ProductListingState.loaded(
          category: category,
          subCategory: subCategory,
        ));
      },
    );
  }
}
