import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../data/models/category_model.dart';

part 'product_listing_state.freezed.dart';

@freezed
class ProductListingState with _$ProductListingState {
  const factory ProductListingState.loading() = _Loading;

  const factory ProductListingState.loaded({
    CategoryModel? category,
    CategoryModel? subCategory,
  }) = _Loaded;

  const factory ProductListingState.error(String message) = _Error;
}
