import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/features/cart/presentation/widgets/floating_cart_wrapper.dart';
import 'package:rozana/routes/app_router.dart';
import '../../../../data/mappers/category_mapper.dart';
import '../../bloc/product_listing_event.dart';

import '../../../categories/presentation/widgets/subcategories_section.dart';
import '../../bloc/product_listing_bloc.dart';
import '../../bloc/product_listing_state.dart';
import '../widgets/product_section.dart';

class ProductListingScreen extends StatefulWidget {
  final String? categoryId;
  final String? categoryName;
  final String? subCategoryId;
  final String? subCategoryName;

  const ProductListingScreen({
    super.key,
    this.categoryId,
    this.categoryName,
    this.subCategoryId,
    this.subCategoryName,
  });

  @override
  State<ProductListingScreen> createState() => _ProductListingScreenState();
}

class _ProductListingScreenState extends State<ProductListingScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Add scroll listener for debugging/pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        debugPrint('Reached 80% of scroll, should load more products');
        // In a real app, you would dispatch a LoadMoreProductsEvent here:
        // context.read<ProductListingBloc>().add(ProductListingEvent.loadMoreProducts());
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return FloatingCartWrapper(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        // Use BlocBuilder to update the AppBar title based on the selected category name
        title: BlocBuilder<ProductListingBloc, ProductListingState>(
          builder: (context, state) {
            return state.when(
              loading: () => Text(
                widget.categoryName ?? 'Products', // Fallback while loading
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              loaded: (category, subCategory) => Text(
                category?.name ?? 'Products',
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              error: (message) => Text(
                'Error', // Or a more descriptive error title
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            );
          },
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () {
            context.pop();
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.black87),
            onPressed: () {
              HapticFeedback.lightImpact();
              // Navigate to search screen
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.black87),
            onPressed: () {
              HapticFeedback.lightImpact();
              // Show filter options
            },
          ),
        ],
      ),
      body: BlocBuilder<ProductListingBloc, ProductListingState>(
        builder: (context, state) {
          return state.when(
            loading: () => const Center(child: CircularProgressIndicator()),
            loaded: (category, subCategory) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Categories section (1 part of flex)
                  Expanded(
                    flex: 1,
                    child: SingleChildScrollView(
                      child: SubCategoriesSection(
                        parentCategory: category != null
                            ? CategoryMapper.toEntity(category)
                            : null,
                        useGridView: false,
                        showAsRow: true,
                        showTitleBar: false,
                        onSubcategorySelected: (subCategory) {
                          if (subCategory != null) {
                            context.read<ProductListingBloc>().add(
                                  ProductListingEvent.selectSubcategory(
                                    subCategory:
                                        CategoryMapper.toModel(subCategory),
                                  ),
                                );
                          }
                        },
                        onSeeAllTap: () {
                          context.push(
                            RouteNames.categories,
                            extra: {
                              'parentCategoryId': category?.id,
                              'parentCategoryName': category?.name,
                            },
                          );
                        },
                      ),
                    ),
                  ),
                  // Products section (3 parts of flex)
                  Expanded(
                    flex: 3,
                    child: ProductsSection(
                      title: subCategory?.name ??
                          'All Products', // Use selectedSubcategoryName from bloc state
                      showSeeAll: false,
                      categoryId:
                          category?.name, // Pass categoryId from bloc state
                      subCategoryId: subCategory?.name ??
                          '', // Pass selectedSubcategoryId from bloc state
                      useGridView: true,
                      imageHeight: 80,
                      imageWidth: 80,
                      height: MediaQuery.of(context).size.height - 150,
                      gridCrossAxisCount: screenWidth > 600 ? 3 : 2,
                      gridChildAspectRatio: 0.7,
                      scrollController: _scrollController,
                      onSeeAllTap: () {
                        context.push(
                          RouteNames.products,
                          extra: {
                            'categoryId': category?.name,
                            'categoryName': subCategory?.name ?? category?.name,
                            'viewAll': true,
                          },
                        );
                      },
                      onProductTap: (product) {
                        context.push(
                          RouteNames.productDetail,
                          extra: {
                            'product': product,
                          },
                        );
                      },
                    ),
                  ),
                ],
              );
            },
            error: (message) => Center(child: Text('Error: $message')),
          );
        },
      ),
    ));
  }
}
