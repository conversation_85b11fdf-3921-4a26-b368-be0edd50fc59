import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rozana/data/models/product_model.dart';
import 'package:rozana/features/products/presentation/widgets/product_card.dart';
import '../../../../data/mappers/product_mapper.dart';

import '../../../../data/services/data_loading_manager.dart';
import '../../../../core/dependency_injection/di_container.dart';
import '../../../../widgets/lazy_loading_widget.dart';
import 'product_skeleton_loader.dart';

class ProductsSection extends StatefulWidget {
  final VoidCallback onSeeAllTap;
  final String title;
  final TextStyle titleStyle;
  final String rightActionText;
  final TextStyle rightActionTextStyle;
  final double height;
  // DataLoadingManager is now accessed directly in the widget
  final bool preloadData;
  final String? categoryId;
  final String? subCategoryId;
  final bool featured;
  final bool useGridView;
  final int gridCrossAxisCount;
  final double gridChildAspectRatio;
  final bool showSeeAll;
  final double imageWidth;
  final double imageHeight;
  final Function(Map<String, dynamic>)? onProductTap;
  final ScrollController? scrollController; // Added scroll controller parameter
  final List<ProductModel>?
      externalProducts; // External products for search results
  final VoidCallback? onLoadMore; // Callback for loading more products

  const ProductsSection({
    super.key,
    required this.onSeeAllTap,
    this.height = 200,
    this.imageWidth = 160,
    this.imageHeight = 160,
    this.title = 'Products',
    this.titleStyle = const TextStyle(
      color: Colors.black87,
      fontSize: 16,
      fontWeight: FontWeight.w600,
    ),
    this.rightActionText = 'See All',
    this.rightActionTextStyle = const TextStyle(
      fontSize: 13,
      fontWeight: FontWeight.w500,
      color: Colors.black54,
    ),
    this.preloadData = true,
    this.categoryId,
    this.subCategoryId,
    this.featured = false,
    this.useGridView = false,
    this.gridCrossAxisCount = 2,
    this.gridChildAspectRatio = 0.7,
    this.externalProducts,
    this.onLoadMore,
    this.showSeeAll = true,
    this.onProductTap,
    this.scrollController, // Added to constructor
  });

  @override
  State<ProductsSection> createState() => _ProductsSectionState();
}

class _ProductsSectionState extends State<ProductsSection> {
  final DataLoadingManager _dataManager = getIt<DataLoadingManager>();
  final List<ProductModel> _products = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _page = 0;

  String? _previousSubCategoryId;

  @override
  void initState() {
    super.initState();
    _previousSubCategoryId = widget.subCategoryId;
    if (widget.externalProducts != null) {
      _products.addAll(widget.externalProducts!);
    } else if (widget.preloadData) {
      _loadProducts();
    }
  }

  @override
  void didUpdateWidget(ProductsSection oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.externalProducts != null) {
      if (oldWidget.externalProducts != widget.externalProducts ||
          (oldWidget.externalProducts?.length !=
              widget.externalProducts?.length)) {
        // Update products with new external data
        setState(() {
          _products.clear();
          _products.addAll(widget.externalProducts!);
          _page = 1; // Set to 1 since we've loaded the first page
        });
        return;
      }
    }

    if (widget.subCategoryId != _previousSubCategoryId) {
      _previousSubCategoryId = widget.subCategoryId;
      _page = 0;
      _products.clear();
      _loadProducts();
    }
  }

  Future<void> _loadProducts() async {
    if (_isLoading) return;
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    if (widget.externalProducts != null) {
      if (_page == 0) {
        setState(() {
          _products.clear();
          _products.addAll(widget.externalProducts!);
          _isLoading = false;
          _hasMore = widget
              .externalProducts!.isNotEmpty; // Has more if we got some products
          _page = 1; // Move to page 1 since we've loaded the first page
        });
      } else if (widget.onLoadMore != null) {
        widget.onLoadMore!();

        if (widget.externalProducts != null &&
            widget.externalProducts!.length > _products.length) {
          setState(() {
            _products.clear();
            _products.addAll(widget.externalProducts!);
          });
        }

        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final products = widget.featured
          ? await _dataManager.loadFeaturedProducts(
              page: _page,
              pageSize: 10,
            )
          : await _dataManager.loadProducts(
              page: _page,
              pageSize: 10,
              subCategoryID: widget.subCategoryId ?? '',
              categoryID: widget.categoryId ?? '',
            );

      setState(() {
        if (_page == 0) {
          _products.clear();
          _products.addAll(
              products.map((json) => ProductModel.fromJson(json)).toList());
        } else {
          _products.addAll(
              products.map((json) => ProductModel.fromJson(json)).toList());
        }

        _isLoading = false;
        _hasMore = products.length >= 10;
        _page = _page + 1;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasMore = false;
      });
    }
  }

  Widget _buildSeeAllHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            widget.title,
            style: widget.titleStyle,
          ),
          TextButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              widget.onSeeAllTap();
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Row(
              children: [
                Text(
                  widget.rightActionText,
                  style: widget.rightActionTextStyle,
                ),
                const SizedBox(width: 2),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 12,
                  color: widget.rightActionTextStyle.color,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.useGridView && widget.scrollController != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showSeeAll) _buildSeeAllHeader(),
          if (widget.showSeeAll) const SizedBox(height: 12),
          Expanded(
            child: _buildProductGrid(),
          ),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showSeeAll) _buildSeeAllHeader(),
          const SizedBox(height: 12),
          widget.useGridView ? _buildProductGrid() : _buildProductList(),
        ],
      );
    }
  }

  Widget _buildProductList() {
    if (_isLoading && _products.isEmpty) {
      return ProductSkeletonLoader(
        useGridView: false,
        showAsRow: false,
        itemCount: 6,
      );
    }
    return HorizontalLazyLoadingList<ProductModel>(
      height: widget.height,
      items: _products,
      isLoading: _isLoading,
      hasMoreData: _hasMore,
      onLoadMore: _hasMore ? _loadProducts : null,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemBuilder: (context, product, index) {
        return Padding(
            padding: EdgeInsets.only(
              right: index == _products.length - 1 ? 0 : 16,
            ),
            child: DiscountedProductCard(
                product: ProductMapper.toEntity(product), isLoading: false)
            // _buildProductCard(product, 160, widget.height - 16),
            );
      },
    );
  }

  Widget _buildProductGrid() {
    if (_isLoading && _products.isEmpty) {
      return ProductSkeletonLoader(
        useGridView: true,
        showAsRow: false,
        gridCrossAxisCount: widget.gridCrossAxisCount,
        gridChildAspectRatio: widget.gridChildAspectRatio,
        itemCount: 6,
      );
    }
    return GridLazyLoadingWidget<ProductModel>(
      items: _products,
      isLoading: _isLoading,
      hasMoreData: _hasMore,
      onLoadMore: _hasMore ? _loadProducts : null,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      scrollController: widget.scrollController,
      // Always use scrollable physics for grid view in product listing
      physics: widget.useGridView
          ? const AlwaysScrollableScrollPhysics()
          : (widget.scrollController != null
              ? const AlwaysScrollableScrollPhysics()
              : null),
      // Only shrink wrap for horizontal lists, not for grid views
      shrinkWrap: !widget.useGridView,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.gridCrossAxisCount,
        childAspectRatio: widget.gridChildAspectRatio,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemBuilder: (context, product, index) {
        return DiscountedProductCard(
            product: ProductMapper.toEntity(product), isLoading: false);
        // _buildProductCard(product, cardWidth, cardHeight);
      },
    );
  }
}
