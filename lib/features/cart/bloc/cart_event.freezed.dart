// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cart_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CartEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CartEventCopyWith<$Res> {
  factory $CartEventCopyWith(CartEvent value, $Res Function(CartEvent) then) =
      _$CartEventCopyWithImpl<$Res, CartEvent>;
}

/// @nodoc
class _$CartEventCopyWithImpl<$Res, $Val extends CartEvent>
    implements $CartEventCopyWith<$Res> {
  _$CartEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CartInitImplCopyWith<$Res> {
  factory _$$CartInitImplCopyWith(
          _$CartInitImpl value, $Res Function(_$CartInitImpl) then) =
      __$$CartInitImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CartInitImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartInitImpl>
    implements _$$CartInitImplCopyWith<$Res> {
  __$$CartInitImplCopyWithImpl(
      _$CartInitImpl _value, $Res Function(_$CartInitImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CartInitImpl implements CartInit {
  const _$CartInitImpl();

  @override
  String toString() {
    return 'CartEvent.init()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CartInitImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return init();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return init?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return init(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return init?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(this);
    }
    return orElse();
  }
}

abstract class CartInit implements CartEvent {
  const factory CartInit() = _$CartInitImpl;
}

/// @nodoc
abstract class _$$CartAddItemImplCopyWith<$Res> {
  factory _$$CartAddItemImplCopyWith(
          _$CartAddItemImpl value, $Res Function(_$CartAddItemImpl) then) =
      __$$CartAddItemImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CartItemModel item});
}

/// @nodoc
class __$$CartAddItemImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartAddItemImpl>
    implements _$$CartAddItemImplCopyWith<$Res> {
  __$$CartAddItemImplCopyWithImpl(
      _$CartAddItemImpl _value, $Res Function(_$CartAddItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? item = null,
  }) {
    return _then(_$CartAddItemImpl(
      item: null == item
          ? _value.item
          : item // ignore: cast_nullable_to_non_nullable
              as CartItemModel,
    ));
  }
}

/// @nodoc

class _$CartAddItemImpl implements CartAddItem {
  const _$CartAddItemImpl({required this.item});

  @override
  final CartItemModel item;

  @override
  String toString() {
    return 'CartEvent.addItem(item: $item)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartAddItemImpl &&
            (identical(other.item, item) || other.item == item));
  }

  @override
  int get hashCode => Object.hash(runtimeType, item);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CartAddItemImplCopyWith<_$CartAddItemImpl> get copyWith =>
      __$$CartAddItemImplCopyWithImpl<_$CartAddItemImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return addItem(item);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return addItem?.call(item);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (addItem != null) {
      return addItem(item);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return addItem(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return addItem?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (addItem != null) {
      return addItem(this);
    }
    return orElse();
  }
}

abstract class CartAddItem implements CartEvent {
  const factory CartAddItem({required final CartItemModel item}) =
      _$CartAddItemImpl;

  CartItemModel get item;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CartAddItemImplCopyWith<_$CartAddItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CartRemoveItemImplCopyWith<$Res> {
  factory _$$CartRemoveItemImplCopyWith(_$CartRemoveItemImpl value,
          $Res Function(_$CartRemoveItemImpl) then) =
      __$$CartRemoveItemImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String itemId});
}

/// @nodoc
class __$$CartRemoveItemImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartRemoveItemImpl>
    implements _$$CartRemoveItemImplCopyWith<$Res> {
  __$$CartRemoveItemImplCopyWithImpl(
      _$CartRemoveItemImpl _value, $Res Function(_$CartRemoveItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? itemId = null,
  }) {
    return _then(_$CartRemoveItemImpl(
      null == itemId
          ? _value.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CartRemoveItemImpl implements CartRemoveItem {
  const _$CartRemoveItemImpl(this.itemId);

  @override
  final String itemId;

  @override
  String toString() {
    return 'CartEvent.removeItem(itemId: $itemId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartRemoveItemImpl &&
            (identical(other.itemId, itemId) || other.itemId == itemId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, itemId);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CartRemoveItemImplCopyWith<_$CartRemoveItemImpl> get copyWith =>
      __$$CartRemoveItemImplCopyWithImpl<_$CartRemoveItemImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return removeItem(itemId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return removeItem?.call(itemId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (removeItem != null) {
      return removeItem(itemId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return removeItem(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return removeItem?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (removeItem != null) {
      return removeItem(this);
    }
    return orElse();
  }
}

abstract class CartRemoveItem implements CartEvent {
  const factory CartRemoveItem(final String itemId) = _$CartRemoveItemImpl;

  String get itemId;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CartRemoveItemImplCopyWith<_$CartRemoveItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CartUpdateQuantityImplCopyWith<$Res> {
  factory _$$CartUpdateQuantityImplCopyWith(_$CartUpdateQuantityImpl value,
          $Res Function(_$CartUpdateQuantityImpl) then) =
      __$$CartUpdateQuantityImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String itemId, int quantity});
}

/// @nodoc
class __$$CartUpdateQuantityImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartUpdateQuantityImpl>
    implements _$$CartUpdateQuantityImplCopyWith<$Res> {
  __$$CartUpdateQuantityImplCopyWithImpl(_$CartUpdateQuantityImpl _value,
      $Res Function(_$CartUpdateQuantityImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? itemId = null,
    Object? quantity = null,
  }) {
    return _then(_$CartUpdateQuantityImpl(
      null == itemId
          ? _value.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$CartUpdateQuantityImpl implements CartUpdateQuantity {
  const _$CartUpdateQuantityImpl(this.itemId, this.quantity);

  @override
  final String itemId;
  @override
  final int quantity;

  @override
  String toString() {
    return 'CartEvent.updateQuantity(itemId: $itemId, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartUpdateQuantityImpl &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity));
  }

  @override
  int get hashCode => Object.hash(runtimeType, itemId, quantity);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CartUpdateQuantityImplCopyWith<_$CartUpdateQuantityImpl> get copyWith =>
      __$$CartUpdateQuantityImplCopyWithImpl<_$CartUpdateQuantityImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return updateQuantity(itemId, quantity);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return updateQuantity?.call(itemId, quantity);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (updateQuantity != null) {
      return updateQuantity(itemId, quantity);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return updateQuantity(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return updateQuantity?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (updateQuantity != null) {
      return updateQuantity(this);
    }
    return orElse();
  }
}

abstract class CartUpdateQuantity implements CartEvent {
  const factory CartUpdateQuantity(final String itemId, final int quantity) =
      _$CartUpdateQuantityImpl;

  String get itemId;
  int get quantity;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CartUpdateQuantityImplCopyWith<_$CartUpdateQuantityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CartClearImplCopyWith<$Res> {
  factory _$$CartClearImplCopyWith(
          _$CartClearImpl value, $Res Function(_$CartClearImpl) then) =
      __$$CartClearImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CartClearImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartClearImpl>
    implements _$$CartClearImplCopyWith<$Res> {
  __$$CartClearImplCopyWithImpl(
      _$CartClearImpl _value, $Res Function(_$CartClearImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CartClearImpl implements CartClear {
  const _$CartClearImpl();

  @override
  String toString() {
    return 'CartEvent.clear()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CartClearImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return clear();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return clear?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (clear != null) {
      return clear();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return clear(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return clear?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (clear != null) {
      return clear(this);
    }
    return orElse();
  }
}

abstract class CartClear implements CartEvent {
  const factory CartClear() = _$CartClearImpl;
}

/// @nodoc
abstract class _$$CartApplyCouponImplCopyWith<$Res> {
  factory _$$CartApplyCouponImplCopyWith(_$CartApplyCouponImpl value,
          $Res Function(_$CartApplyCouponImpl) then) =
      __$$CartApplyCouponImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String code});
}

/// @nodoc
class __$$CartApplyCouponImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartApplyCouponImpl>
    implements _$$CartApplyCouponImplCopyWith<$Res> {
  __$$CartApplyCouponImplCopyWithImpl(
      _$CartApplyCouponImpl _value, $Res Function(_$CartApplyCouponImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
  }) {
    return _then(_$CartApplyCouponImpl(
      null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CartApplyCouponImpl implements CartApplyCoupon {
  const _$CartApplyCouponImpl(this.code);

  @override
  final String code;

  @override
  String toString() {
    return 'CartEvent.applyCoupon(code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartApplyCouponImpl &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, code);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CartApplyCouponImplCopyWith<_$CartApplyCouponImpl> get copyWith =>
      __$$CartApplyCouponImplCopyWithImpl<_$CartApplyCouponImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return applyCoupon(code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return applyCoupon?.call(code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (applyCoupon != null) {
      return applyCoupon(code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return applyCoupon(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return applyCoupon?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (applyCoupon != null) {
      return applyCoupon(this);
    }
    return orElse();
  }
}

abstract class CartApplyCoupon implements CartEvent {
  const factory CartApplyCoupon(final String code) = _$CartApplyCouponImpl;

  String get code;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CartApplyCouponImplCopyWith<_$CartApplyCouponImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CartRemoveCouponImplCopyWith<$Res> {
  factory _$$CartRemoveCouponImplCopyWith(_$CartRemoveCouponImpl value,
          $Res Function(_$CartRemoveCouponImpl) then) =
      __$$CartRemoveCouponImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CartRemoveCouponImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartRemoveCouponImpl>
    implements _$$CartRemoveCouponImplCopyWith<$Res> {
  __$$CartRemoveCouponImplCopyWithImpl(_$CartRemoveCouponImpl _value,
      $Res Function(_$CartRemoveCouponImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CartRemoveCouponImpl implements CartRemoveCoupon {
  const _$CartRemoveCouponImpl();

  @override
  String toString() {
    return 'CartEvent.removeCoupon()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CartRemoveCouponImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return removeCoupon();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return removeCoupon?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (removeCoupon != null) {
      return removeCoupon();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return removeCoupon(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return removeCoupon?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (removeCoupon != null) {
      return removeCoupon(this);
    }
    return orElse();
  }
}

abstract class CartRemoveCoupon implements CartEvent {
  const factory CartRemoveCoupon() = _$CartRemoveCouponImpl;
}

/// @nodoc
abstract class _$$CartImportDataImplCopyWith<$Res> {
  factory _$$CartImportDataImplCopyWith(_$CartImportDataImpl value,
          $Res Function(_$CartImportDataImpl) then) =
      __$$CartImportDataImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String jsonData});
}

/// @nodoc
class __$$CartImportDataImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartImportDataImpl>
    implements _$$CartImportDataImplCopyWith<$Res> {
  __$$CartImportDataImplCopyWithImpl(
      _$CartImportDataImpl _value, $Res Function(_$CartImportDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jsonData = null,
  }) {
    return _then(_$CartImportDataImpl(
      null == jsonData
          ? _value.jsonData
          : jsonData // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CartImportDataImpl implements CartImportData {
  const _$CartImportDataImpl(this.jsonData);

  @override
  final String jsonData;

  @override
  String toString() {
    return 'CartEvent.importData(jsonData: $jsonData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartImportDataImpl &&
            (identical(other.jsonData, jsonData) ||
                other.jsonData == jsonData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, jsonData);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CartImportDataImplCopyWith<_$CartImportDataImpl> get copyWith =>
      __$$CartImportDataImplCopyWithImpl<_$CartImportDataImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return importData(jsonData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return importData?.call(jsonData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (importData != null) {
      return importData(jsonData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return importData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return importData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (importData != null) {
      return importData(this);
    }
    return orElse();
  }
}

abstract class CartImportData implements CartEvent {
  const factory CartImportData(final String jsonData) = _$CartImportDataImpl;

  String get jsonData;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CartImportDataImplCopyWith<_$CartImportDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CartLoadDefaultAddressImplCopyWith<$Res> {
  factory _$$CartLoadDefaultAddressImplCopyWith(
          _$CartLoadDefaultAddressImpl value,
          $Res Function(_$CartLoadDefaultAddressImpl) then) =
      __$$CartLoadDefaultAddressImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CartLoadDefaultAddressImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartLoadDefaultAddressImpl>
    implements _$$CartLoadDefaultAddressImplCopyWith<$Res> {
  __$$CartLoadDefaultAddressImplCopyWithImpl(
      _$CartLoadDefaultAddressImpl _value,
      $Res Function(_$CartLoadDefaultAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CartLoadDefaultAddressImpl implements CartLoadDefaultAddress {
  const _$CartLoadDefaultAddressImpl();

  @override
  String toString() {
    return 'CartEvent.loadDefaultAddress()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartLoadDefaultAddressImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return loadDefaultAddress();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return loadDefaultAddress?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (loadDefaultAddress != null) {
      return loadDefaultAddress();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return loadDefaultAddress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return loadDefaultAddress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (loadDefaultAddress != null) {
      return loadDefaultAddress(this);
    }
    return orElse();
  }
}

abstract class CartLoadDefaultAddress implements CartEvent {
  const factory CartLoadDefaultAddress() = _$CartLoadDefaultAddressImpl;
}

/// @nodoc
abstract class _$$CartSelectAddressImplCopyWith<$Res> {
  factory _$$CartSelectAddressImplCopyWith(_$CartSelectAddressImpl value,
          $Res Function(_$CartSelectAddressImpl) then) =
      __$$CartSelectAddressImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AddressModel address});
}

/// @nodoc
class __$$CartSelectAddressImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartSelectAddressImpl>
    implements _$$CartSelectAddressImplCopyWith<$Res> {
  __$$CartSelectAddressImplCopyWithImpl(_$CartSelectAddressImpl _value,
      $Res Function(_$CartSelectAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
  }) {
    return _then(_$CartSelectAddressImpl(
      null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as AddressModel,
    ));
  }
}

/// @nodoc

class _$CartSelectAddressImpl implements CartSelectAddress {
  const _$CartSelectAddressImpl(this.address);

  @override
  final AddressModel address;

  @override
  String toString() {
    return 'CartEvent.selectAddress(address: $address)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartSelectAddressImpl &&
            (identical(other.address, address) || other.address == address));
  }

  @override
  int get hashCode => Object.hash(runtimeType, address);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CartSelectAddressImplCopyWith<_$CartSelectAddressImpl> get copyWith =>
      __$$CartSelectAddressImplCopyWithImpl<_$CartSelectAddressImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return selectAddress(address);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return selectAddress?.call(address);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (selectAddress != null) {
      return selectAddress(address);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return selectAddress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return selectAddress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (selectAddress != null) {
      return selectAddress(this);
    }
    return orElse();
  }
}

abstract class CartSelectAddress implements CartEvent {
  const factory CartSelectAddress(final AddressModel address) =
      _$CartSelectAddressImpl;

  AddressModel get address;

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CartSelectAddressImplCopyWith<_$CartSelectAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CartClearAddressImplCopyWith<$Res> {
  factory _$$CartClearAddressImplCopyWith(_$CartClearAddressImpl value,
          $Res Function(_$CartClearAddressImpl) then) =
      __$$CartClearAddressImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CartClearAddressImplCopyWithImpl<$Res>
    extends _$CartEventCopyWithImpl<$Res, _$CartClearAddressImpl>
    implements _$$CartClearAddressImplCopyWith<$Res> {
  __$$CartClearAddressImplCopyWithImpl(_$CartClearAddressImpl _value,
      $Res Function(_$CartClearAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of CartEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CartClearAddressImpl implements CartClearAddress {
  const _$CartClearAddressImpl();

  @override
  String toString() {
    return 'CartEvent.clearAddress()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CartClearAddressImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function(CartItemModel item) addItem,
    required TResult Function(String itemId) removeItem,
    required TResult Function(String itemId, int quantity) updateQuantity,
    required TResult Function() clear,
    required TResult Function(String code) applyCoupon,
    required TResult Function() removeCoupon,
    required TResult Function(String jsonData) importData,
    required TResult Function() loadDefaultAddress,
    required TResult Function(AddressModel address) selectAddress,
    required TResult Function() clearAddress,
  }) {
    return clearAddress();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function(CartItemModel item)? addItem,
    TResult? Function(String itemId)? removeItem,
    TResult? Function(String itemId, int quantity)? updateQuantity,
    TResult? Function()? clear,
    TResult? Function(String code)? applyCoupon,
    TResult? Function()? removeCoupon,
    TResult? Function(String jsonData)? importData,
    TResult? Function()? loadDefaultAddress,
    TResult? Function(AddressModel address)? selectAddress,
    TResult? Function()? clearAddress,
  }) {
    return clearAddress?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function(CartItemModel item)? addItem,
    TResult Function(String itemId)? removeItem,
    TResult Function(String itemId, int quantity)? updateQuantity,
    TResult Function()? clear,
    TResult Function(String code)? applyCoupon,
    TResult Function()? removeCoupon,
    TResult Function(String jsonData)? importData,
    TResult Function()? loadDefaultAddress,
    TResult Function(AddressModel address)? selectAddress,
    TResult Function()? clearAddress,
    required TResult orElse(),
  }) {
    if (clearAddress != null) {
      return clearAddress();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CartInit value) init,
    required TResult Function(CartAddItem value) addItem,
    required TResult Function(CartRemoveItem value) removeItem,
    required TResult Function(CartUpdateQuantity value) updateQuantity,
    required TResult Function(CartClear value) clear,
    required TResult Function(CartApplyCoupon value) applyCoupon,
    required TResult Function(CartRemoveCoupon value) removeCoupon,
    required TResult Function(CartImportData value) importData,
    required TResult Function(CartLoadDefaultAddress value) loadDefaultAddress,
    required TResult Function(CartSelectAddress value) selectAddress,
    required TResult Function(CartClearAddress value) clearAddress,
  }) {
    return clearAddress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CartInit value)? init,
    TResult? Function(CartAddItem value)? addItem,
    TResult? Function(CartRemoveItem value)? removeItem,
    TResult? Function(CartUpdateQuantity value)? updateQuantity,
    TResult? Function(CartClear value)? clear,
    TResult? Function(CartApplyCoupon value)? applyCoupon,
    TResult? Function(CartRemoveCoupon value)? removeCoupon,
    TResult? Function(CartImportData value)? importData,
    TResult? Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult? Function(CartSelectAddress value)? selectAddress,
    TResult? Function(CartClearAddress value)? clearAddress,
  }) {
    return clearAddress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CartInit value)? init,
    TResult Function(CartAddItem value)? addItem,
    TResult Function(CartRemoveItem value)? removeItem,
    TResult Function(CartUpdateQuantity value)? updateQuantity,
    TResult Function(CartClear value)? clear,
    TResult Function(CartApplyCoupon value)? applyCoupon,
    TResult Function(CartRemoveCoupon value)? removeCoupon,
    TResult Function(CartImportData value)? importData,
    TResult Function(CartLoadDefaultAddress value)? loadDefaultAddress,
    TResult Function(CartSelectAddress value)? selectAddress,
    TResult Function(CartClearAddress value)? clearAddress,
    required TResult orElse(),
  }) {
    if (clearAddress != null) {
      return clearAddress(this);
    }
    return orElse();
  }
}

abstract class CartClearAddress implements CartEvent {
  const factory CartClearAddress() = _$CartClearAddressImpl;
}
