import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:rozana/domain/entities/banner_entity.dart';
// import 'package:rozana/data/models/product_model.dart';
import 'package:rozana/features/home/<USER>/home%20bloc/home_bloc.dart';
import 'package:rozana/features/home/<USER>/widgets/section_categories.dart';
import 'package:rozana/features/home/<USER>/widgets/section_most_bought.dart';
import 'package:rozana/features/home/<USER>/widgets/section_most_popular.dart';
import 'package:rozana/features/home/<USER>/widgets/section_previously_bought.dart';
import 'package:rozana/features/home/<USER>/widgets/section_stores.dart';
import 'package:rozana/features/home/<USER>/widgets/section_superpack.dart';
import 'package:rozana/features/home/<USER>/widgets/section_top_categories.dart';
import 'package:rozana/widgets/rozana_logo_image.dart';

import '../../../../core/themes/color_schemes.dart';
import '../widgets/custom_search_appbar.dart';
import 'package:lottie/lottie.dart';

// import '../widgets/section_banner.dart';
// import '../widgets/section_combo_deals.dart';
import '../widgets/section_deals.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.surface,
        body: RefreshIndicator(
          onRefresh: () async {
            context.read<HomeBloc>().add(HomeEvent.loadHomeData());
          },
          edgeOffset: 160,
          child: CustomScrollView(
            controller: context.read<HomeBloc>().scrollController,
            slivers: [
              BlocBuilder<HomeBloc, HomeState>(
                buildWhen: (previous, current) =>
                    previous.isScrolled != current.isScrolled,
                builder: (context, state) {
                  return SliverAppBar(
                    expandedHeight: MediaQuery.of(context).size.width + 30,
                    toolbarHeight: 115,
                    pinned: true,
                    elevation: state.isScrolled ? 4 : 0,
                    backgroundColor: AppColors.primaryAverage,
                    flexibleSpace: FlexibleSpaceBar(
                      background: Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          Stack(
                            children: [
                              Container(
                                color: AppColors.surface,
                                padding: const EdgeInsets.only(bottom: 80),
                              ),
                              CustomPaint(
                                painter: CurvedBottomPainter(
                                    lightColor: state.isScrolled
                                        ? AppColors.primaryAverage
                                        : AppColors.primary,
                                    darkColor: state.isScrolled
                                        ? AppColors.primaryAverage
                                        : AppColors.primaryDark),
                                child: state.isScrolled
                                    ? SizedBox(
                                        width: double.infinity,
                                        height:
                                            MediaQuery.of(context).size.width)
                                    : Lottie.asset(
                                        'assets/lotties/home_animation.json',
                                        width:
                                            MediaQuery.of(context).size.width,
                                        fit: BoxFit.cover,
                                      ),
                              ),
                            ],
                          ),
                          TopCategoriesSection(),
                        ],
                      ),
                      expandedTitleScale: 1,
                      collapseMode: CollapseMode.pin,
                    ),
                    titleSpacing: 0,
                    title: Container(
                        decoration: BoxDecoration(
                          color: state.isScrolled
                              ? AppColors.primaryAverage
                              : Colors.transparent,
                        ),
                        child: CustomSliverAppBarContent()),
                  );
                },
              ),
              SliverList(
                  delegate: SliverChildListDelegate([
                BlocBuilder<HomeBloc, HomeState>(
                  buildWhen: (previous, current) =>
                      (previous is! HomeError) || (current is! HomeError),
                  builder: (context, state) {
                    return state.maybeMap(
                      error: (value) =>
                          _buildErrorState(value.message, context),
                      orElse: () => HomeBody(),
                    );
                  },
                ),
              ])),
            ],
          ),
        ));
  }
}

class HomeBody extends StatelessWidget {
  const HomeBody({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 15),

        // Previiously Bought
        PreviouslyBoughtSection(),

        // Most popular
        MostPopularSection(),

        // Banners section
        // BlocBuilder<HomeBloc, HomeState>(
        //   buildWhen: (previous, current) {
        //     if (previous is! HomeLoaded) {
        //       return true;
        //     }
        //     if (current is HomeLoaded) {
        //       return previous.banners != current.banners;
        //     }
        //     return false; // Don’t rebuild for other transitions
        //   },
        //   builder: (context, state) {
        //     List<BannerEntity>? banners =
        //         state.mapOrNull(loaded: (value) => value.banners);
        //     return BannerSection(banners: banners);
        //   },
        // ),

        // Combo Deal section
        // ComboDealSection(
        //   productCombos: [
        //     ProductComboModel(
        //         products: [ProductModel(), ProductModel(), ProductModel()]),
        //     ProductComboModel(products: [ProductModel(), ProductModel()]),
        //     ProductComboModel(
        //         products: [ProductModel(), ProductModel(), ProductModel()])
        //   ],
        // ),

        //Deals section
        DealsSection(),

        // Most Bought
        MostBoughtSection(),

        //Shop by store section
        StoreSection(),

        //SuperPack section
        SuperPackSection(),

        //Categories section
        CategoriesSection(),

        //Rozana logo
        RozanaLogoImage(
          padding: EdgeInsets.only(top: 30, bottom: 40),
          width: MediaQuery.of(context).size.width * 0.8,
        ),
      ],
    );
  }
}

Widget _buildErrorState(String message, BuildContext context) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.error_outline, color: Colors.red, size: 50),
        const SizedBox(height: 10),
        Text(message, textAlign: TextAlign.center),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: () {
            context.read<HomeBloc>().add(const HomeEvent.loadHomeData());
          },
          child: const Text('Retry'),
        ),
      ],
    ),
  );
}

class CurvedBottomPainter extends CustomPainter {
  final Color lightColor;
  final Color darkColor;

  CurvedBottomPainter({
    super.repaint,
    this.lightColor = AppColors.primary,
    this.darkColor = AppColors.primaryDark,
  });
  @override
  void paint(Canvas canvas, Size size) {
    final Rect rect = Rect.fromLTWH(0, 0, size.width, size.height);

    final Gradient gradient = RadialGradient(
      center: Alignment.center,
      radius: 0.8,
      colors: [
        lightColor, // light center
        darkColor
      ],
      stops: [0.01, 1.0],
    );

    final paint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(0, 0)
      ..lineTo(0, size.height - 40)
      ..quadraticBezierTo(
        size.width / 2,
        size.height + 40,
        size.width,
        size.height - 40,
      )
      ..lineTo(size.width, 0)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
