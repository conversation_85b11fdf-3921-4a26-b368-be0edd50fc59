import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/widgets/viewall_category_title.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../bloc/home bloc/home_bloc.dart';
import '../../../categories/presentation/widgets/categoy_card.dart';

class CategoriesSection extends StatelessWidget {
  const CategoriesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        List<CategoryEntity> prevCategories =
            previous.mapOrNull(loaded: (value) => value.categories) ?? [];

        List<CategoryEntity> currCategories =
            current.mapOrNull(loaded: (value) => value.categories) ?? [];

        return prevCategories != currCategories;
      },
      builder: (context, state) {
        List<CategoryEntity>? categories =
            state.mapOrNull(loaded: (value) => value.categories);
        return Visibility(
          visible: categories?.isNotEmpty ?? false,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                    left: AppDimensions.screenHzPadding, bottom: 15),
                child: ViewAllCategoryTitle(title: 'Explore Categories'),
              ),
              GridView.builder(
                shrinkWrap: true,
                primary: false,
                padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.screenHzPadding),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  mainAxisSpacing: 20,
                  crossAxisSpacing: 20,
                  childAspectRatio: 0.8,
                ),
                itemCount: categories?.length ?? 0,
                itemBuilder: (ctx, index) {
                  CategoryEntity? category = categories?[index];
                  return CategoryCard(
                    category: category,
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
