import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rozana/domain/entities/banner_entity.dart';

import '../../../../widgets/app_carousel.dart';

class BannerSection extends StatelessWidget {
  const BannerSection({super.key, required this.banners});

  final List<BannerEntity>? banners;

  @override
  Widget build(BuildContext context) {
    if (banners?.isEmpty ?? false) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 30),
      child: AppCarousel<BannerEntity?>(
        items: banners ??
            [
              const BannerEntity(id: '', imageUrl: ''),
              const BannerEntity(id: '', imageUrl: '')
            ],
        height: 180,
        viewportFraction: 0.92,
        borderRadius: 12,
        autoPlayInterval: const Duration(seconds: 4),
        indicatorType: CarouselIndicatorType.dottedBar,
        itemBuilder: (context, banner, index) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Stack(
              fit: StackFit.expand,
              children: [
                Image.network(
                  banner?.imageUrl ?? '',
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    color: Colors.grey[200],
                    child: Center(
                      child: Icon(
                        Icons.image_not_supported_outlined,
                        color: Colors.grey[400],
                        size: 32,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
        onItemTap: (index) {
          HapticFeedback.lightImpact();
          BannerEntity? banner = banners?[index];
          debugPrint('Banner tapped: ${banner?.categoryName} - ${banner?.id}');
        },
      ),
    );
  }
}
