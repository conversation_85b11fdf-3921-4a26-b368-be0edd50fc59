import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../../widgets/shimmer_widgets.dart';
import '../../bloc/home bloc/home_bloc.dart';
import 'product_grid.dart';

class MostPopularSection extends StatelessWidget {
  const MostPopularSection({super.key});

  @override
  Widget build(BuildContext context) {
    int selectedItem = 0;
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        if (previous is! HomeLoaded) {
          return true;
        }
        if (current is HomeLoaded) {
          return previous.mostPopular != current.mostPopular;
        }
        return false; // Don’t rebuild for other transitions
      },
      builder: (context, state) {
        List<ProductEntity>? mostPopular =
            state.mapOrNull(loaded: (value) => value.mostPopular);
        return Visibility(
          visible: mostPopular != null,
          replacement: Column(
            children: [
              SizedBox(height: 30),
              Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.screenHzPadding),
                  child: ShimmerBox(height: 80, radius: 20)),
              SizedBox(height: 20),
              ProductGrid(productList: null),
            ],
          ),
          child: Visibility(
              visible: mostPopular?.isNotEmpty ?? false,
              child: Container(
                width: double.infinity,
                margin: EdgeInsets.only(top: 30),
                padding: EdgeInsets.fromLTRB(0, 20, 0, 20),
                decoration: BoxDecoration(
                  color: Color(0xFF00BFFF),
                  image: DecorationImage(
                      fit: BoxFit.fill,
                      image:
                          AssetImage('assets/images/background_texture_1.png')),
                ),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.screenHzPadding),
                      child: Image.asset(
                        'assets/images/most_popular_text.png',
                        width: MediaQuery.of(context).size.width * 0.7,
                      ),
                    ),
                    Container(
                      height: 54,
                      width: double.infinity,
                      margin: EdgeInsets.fromLTRB(AppDimensions.screenHzPadding,
                          20, AppDimensions.screenHzPadding, 15),
                      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 5),
                      decoration: BoxDecoration(
                        color: Color(0xFF0A98D9),
                        borderRadius: BorderRadius.circular(70),
                      ),
                      // child: StatefulBuilder(builder: (context, setState) {
                      //   return Row(
                      //     children: List.generate(3, (i) {
                      //       // List<String> items = ['Daily', 'Weekly', 'Monthly'];
                      //       bool isSelected = selectedItem == i;
                      //       return Expanded(
                      //           child: GestureDetector(
                      //         onTap: () {
                      //           HapticFeedback.lightImpact();
                      //           setState(() {
                      //             selectedItem = i;
                      //           });
                      //         },
                      //         child: Container(
                      //           decoration: BoxDecoration(
                      //               color: isSelected
                      //                   ? AppColors.surface
                      //                   : Color(0xFF0A98D9),
                      //               borderRadius: BorderRadius.circular(30)),
                      //           alignment: Alignment.center,
                      //           child: CustomText(
                      //             items[i],
                      //             fontWeight: FontWeight.w800,
                      //             color: isSelected
                      //                 ? AppColors.textPrimary
                      //                 : AppColors.surface,
                      //           ),
                      //         ),
                      //       ));
                      //     }),
                      //   );
                      // }),
                    ),
                    ProductGrid(productList: mostPopular ?? []),
                  ],
                ),
              )),
        );
      },
    );
  }
}
