import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/features/categories/presentation/widgets/categoy_card.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../bloc/home bloc/home_bloc.dart';

class TopCategoriesSection extends StatelessWidget {
  const TopCategoriesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        height: 160,
        width: double.infinity,
        child: Padding(
          padding: const EdgeInsets.only(bottom: 15),
          child: BlocBuilder<HomeBloc, HomeState>(
            buildWhen: (previous, current) {
              List<CategoryEntity> prevCategories =
                  previous.mapOrNull(loaded: (value) => value.categories) ?? [];

              List<CategoryEntity> currCategories =
                  current.mapOrNull(loaded: (value) => value.categories) ?? [];

              return prevCategories != currCategories;
            },
            builder: (context, state) {
              List<CategoryEntity> categories = state.maybeMap(
                  loaded: (value) => value.categories ?? [], orElse: () => []);
              return ListView.separated(
                padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.screenHzPadding),
                scrollDirection: Axis.horizontal,
                itemCount: categories.length,
                itemBuilder: (ctx, index) {
                  return CategoryOfferCard(category: categories[index]);
                },
                separatorBuilder: (ctx, index) => SizedBox(width: 10),
              );
            },
          ),
        ));
  }
}
