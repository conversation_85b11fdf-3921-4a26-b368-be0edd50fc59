import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/features/categories/presentation/widgets/categoy_card.dart';
import 'package:rozana/widgets/viewall_category_title.dart';

import '../../../../core/utils/app_dimensions.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../routes/app_router.dart';
import '../../bloc/home bloc/home_bloc.dart';

class StoreSection extends StatelessWidget {
  const StoreSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        List<CategoryEntity> prevCategories =
            previous.mapOrNull(loaded: (value) => value.categories) ?? [];

        List<CategoryEntity> currCategories =
            current.mapOrNull(loaded: (value) => value.categories) ?? [];

        return prevCategories != currCategories;
      },
      builder: (context, state) {
        List<CategoryEntity>? categories =
            state.mapOrNull(loaded: (value) => value.categories);
        return Visibility(
          visible: categories?.isNotEmpty ?? false,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                    left: AppDimensions.screenHzPadding, top: 30, bottom: 8),
                child: ViewAllCategoryTitle(
                    title: 'Shop by store',
                    onTap: () {
                      context.push(RouteNames.categories);
                    }),
              ),
              SizedBox(
                height: 135,
                child: ListView.separated(
                  padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.screenHzPadding),
                  scrollDirection: Axis.horizontal,
                  itemCount: categories?.length ?? 0,
                  itemBuilder: (ctx, index) {
                    CategoryEntity? category = categories?[index];
                    return CategoryCard(category: category);
                  },
                  separatorBuilder: (ctx, index) => SizedBox(width: 10),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
