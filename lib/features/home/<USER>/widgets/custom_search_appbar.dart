import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/blocs/language_bloc/language_bloc.dart';
import 'package:rozana/core/extensions/localization_extension.dart';
import 'package:rozana/widgets/custom_text.dart';

import '../../../../routes/app_router.dart';
import '../../../cart/bloc/cart_bloc.dart';
import '../../../cart/bloc/cart_state.dart';
import 'refreshable_address_bar.dart';

class CustomSliverAppBarContent extends StatelessWidget {
  const CustomSliverAppBarContent({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Column(
        mainAxisSize: MainAxisSize.min, // Make column as small as possible
        children: [
          // TOP LOCATION/DELIVERY BAR
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: RefreshableAddressBar(
                    backgroundColor: Colors.transparent,
                    textColor: AppColors.white,
                    onAddressChanged: (address) {}),
              ),
              const SizedBox(width: 10),
              BlocBuilder<LanguageBloc, LanguageState>(
                builder: (context, languageState) {
                  return Tooltip(
                    message: context.l10n.language,
                    child: InkWell(
                      onTap: () {
                        // Toggle between English and Hindi
                        context
                            .read<LanguageBloc>()
                            .add(const LanguageEvent.toggleLanguage());
                        HapticFeedback.lightImpact();

                        // Show feedback to user
                        final currentLanguage = languageState.mapOrNull(
                              loaded: (state) => state.languageCode,
                            ) ??
                            'en';
                        final newLanguage =
                            currentLanguage == 'en' ? 'hi' : 'en';
                        final languageName =
                            newLanguage == 'en' ? 'English' : 'हिंदी';

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Language switched to $languageName',
                              style: const TextStyle(color: AppColors.white),
                            ),
                            backgroundColor: AppColors.primary,
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Image.asset(
                          'assets/icons/language_icon.png',
                          height: 20,
                          width: 20,
                        ),
                      ),
                    ),
                  );
                },
              ),
              Tooltip(
                message: 'Cart',
                child: InkWell(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    context.push(RouteNames.cart);
                  },
                  child: Stack(
                    alignment: Alignment.topRight,
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(10, 10, 8, 10),
                        child: Image.asset(
                          'assets/icons/cart_icon.png',
                          height: 20,
                          width: 20,
                        ),
                      ),
                      BlocBuilder<CartBloc, CartState>(
                        builder: (context, state) {
                          int totalCart = state.cart.totalItems;
                          return Visibility(
                            visible: totalCart > 0,
                            child: CircleAvatar(
                              radius: 10,
                              backgroundColor: AppColors.primaryDark,
                              child: CircleAvatar(
                                radius: 7.8,
                                backgroundColor: Color(0xFFFEDA00),
                                child: FittedBox(
                                  child: CustomText(
                                    totalCart.toString(),
                                    color: Color(0xFF0C0423),
                                    fontSize: 8,
                                    fontWeight: FontWeight.w800,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.screenHzPadding),
            ],
          ),

          // SEARCH BAR
          GestureDetector(
            onTap: () {
              context.push('${RouteNames.search}?initialQuery=');
            },
            child: Container(
              height: 44,
              margin: EdgeInsets.symmetric(
                  vertical: 10, horizontal: AppDimensions.screenHzPadding),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(50),
              ),
              child: Row(
                children: [
                  Image.asset(
                    'assets/icons/search_icon.png',
                    width: 14,
                    height: 14,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: CustomText(
                      'Search for Store / Groceries & Essentials.....',
                      color: AppColors.textGrey,
                      fontSize: 12,
                      maxLines: 1,
                      fontWeight: FontWeight.w500,
                      overflow: TextOverflow.ellipsis,
                      textHeight: 0.9,
                    ),
                  ),
                  Image.asset(
                    'assets/icons/mic_icon.png',
                    width: 14,
                    height: 14,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 5),
        ],
      ),
    );
  }
}
