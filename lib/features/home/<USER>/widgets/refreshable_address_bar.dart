import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/utils/app_dimensions.dart';
import 'package:rozana/core/utils/helpers.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/routes/app_router.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../widgets/custom_text.dart';

class RefreshableAddressBar extends StatelessWidget {
  final double height;
  final EdgeInsetsGeometry padding;
  final Color backgroundColor;
  final Color textColor;
  final ValueChanged<AddressModel?>? onAddressChanged;

  const RefreshableAddressBar({
    super.key,
    this.height = 60,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    this.backgroundColor = Colors.transparent,
    this.textColor = Colors.white,
    this.onAddressChanged,
  });

  void _handleTap(BuildContext context) async {
    // Navigate to address list or add address screen based on saved addresses
    final addressService = context.read<LocationBloc>().addressService;
    final addresses = await addressService.getAllAddresses();

    // Check if the widget is still mounted before using context
    if (context.mounted) {
      context.push(
          addresses.isEmpty ? RouteNames.addAddress : RouteNames.addresses);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<LocationBloc, LocationState>(
      listener: (context, state) {
        state.maybeWhen(
          loaded: (address) {
            if (onAddressChanged != null) {
              onAddressChanged!(address);
            }
          },
          orElse: () {},
        );
      },
      builder: (context, state) {
        bool isLoading = true;
        String address = '';
        String addressType = '';
        state.maybeWhen(
          loaded: (addressData) {
            address = addressData.fullAddress ?? '';
            addressType = addressData.addressType ?? 'Home';
            isLoading = false;
          },
          loading: () {
            address = 'Select an address';
            isLoading = true;
          },
          orElse: () {
            address = 'Select an address';
            isLoading = false;
          },
        );

        return InkWell(
          onTap: () => _handleTap(context),
          child: Container(
            height: height,
            padding: AppDimensions.screenPadding.copyWith(top: 0, bottom: 0),
            color: backgroundColor,
            child: Row(
              children: [
                Image.asset(
                  'assets/icons/home_location.png',
                  height: 30,
                  width: 30,
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        '${TextFormatter.formatToUiText(addressType)} - Deliver in 8 Mins',
                        fontSize: 16,
                        fontWeight: FontWeight.w800,
                        color: AppColors.background,
                      ),
                      SizedBox(height: 4),
                      CustomText(
                        address,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        overflow: TextOverflow.ellipsis,
                        color: AppColors.background,
                      ),
                    ],
                  ),
                ),
                Tooltip(
                  message: 'Refresh location',
                  child: InkWell(
                    splashColor: Colors.transparent,
                    onTap: isLoading
                        ? null
                        : () => context
                            .read<LocationBloc>()
                            .add(const LocationEvent.refreshLocation()),
                    child: Padding(
                      padding: const EdgeInsets.only(left: 10),
                      child: CircleAvatar(
                        backgroundColor: AppColors.primaryDark,
                        radius: 15,
                        child: isLoading
                            ? SizedBox(
                                width: 14,
                                height: 14,
                                child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: AppColors.background),
                              )
                            : Icon(Icons.refresh,
                                size: 16, color: AppColors.background),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
