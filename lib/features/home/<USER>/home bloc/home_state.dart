import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../domain/entities/banner_entity.dart';
import '../../../../domain/entities/category_entity.dart';
import '../../../../domain/entities/product_entity.dart';

part 'home_state.freezed.dart'; // This file will be generated

@freezed
class HomeState with _$HomeState {
  const factory HomeState.initial({@Default(false) bool isScrolled}) =
      HomeInitial;
  const factory HomeState.loaded({
    required List<CategoryEntity>? categories,
    required List<ProductEntity>? previouslyBought,
    required List<ProductEntity>? mostPopular,
    required List<ProductEntity>? mostBought,
    required List<BannerEntity>? banners,
    required bool isScrolled,
  }) = HomeLoaded;
  const factory HomeState.error(
      {required String message, required bool isScrolled}) = HomeError;
  const factory HomeState.deepLink(
      {required bool isScrolled,
      required String route,
      @Default({}) Map<String, dynamic> args}) = HomeDeepLink;
}
