export 'search_event.dart';
export 'search_state.dart';

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/utils/debouncer.dart';
import '../../../../core/utils/text_field_manager.dart';
import '../../services/typesense_service.dart';
import 'search_event.dart';
import 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final TypesenseService _searchService;

  final int _pageSize = 30;
  static bool enableSearchPagination = true;

  final Debouncer _suggestionDebouncer = Debouncer(milliseconds: 200);
  final Debouncer _searchDebouncer = Debouncer(milliseconds: 1000);

  static TextFieldManager? searchFieldManager;
  static ScrollController? scrollController;

  SearchBloc(this._searchService) : super(SearchState.initial()) {
    searchFieldManager = TextFieldManager();
    scrollController = ScrollController();
    on<SearchEvent>(
      (event, emit) => event.map(
        init: (e) async => await _onInit(e.initialQuery, emit),
        search: (e) async => await _onSearch(e.query, e.submit, emit),
        loadMore: (e) async => await _onLoadMore(emit),
        clearSearch: (e) async => await _onClearSearch(emit),
        clearRecent: (e) async => await _onClearRecent(emit),
        removeRecent: (e) async => await _onRemoveRecent(e.index, emit),
        selectRecent: (e) async => await _onSelectRecent(e.query, emit),
        selectSuggestion: (e) async =>
            await _onSelectSuggestion(e.suggestion, emit),
        inputChange: (e) async => await _onInputChange(e.query, emit),
        updateSuggestions: (e) async =>
            await _onUpdateSuggestions(e.query, emit),
        clearState: (e) async => await _onClearState(emit),
      ),
    );
  }

  Future<void> _onInit(String initialQuery, Emitter<SearchState> emit) async {
    try {
      if (initialQuery.isNotEmpty) {
        add(SearchEvent.search(initialQuery));
      }
      searchFieldManager?.focusNode.requestFocus();

      final json = AppPreferences.getRecentSearches();
      if (json != null) {
        final decoded = List<String>.from(jsonDecode(json));
        emit(state.copyWith(recentSearches: decoded));
      }
    } catch (_) {}
  }

  Future<void> _onSearch(
      String query, bool submitSearch, Emitter<SearchState> emit) async {
    emit(state.copyWith(
        isLoading: true,
        query: query,
        page: 1,
        hasMore: enableSearchPagination));

    try {
      final results = await _searchService.globalSearch(
        query: query,
        page: 1,
        pageSize: _pageSize,
      );

      emit(state.copyWith(
        isLoading: false,
        products: results['products'] ?? [],
        categories: results['categories'] ?? [],
        subcategories: results['subcategories'] ?? [],
        hasMore: enableSearchPagination &&
            ((results['products']?.length ?? 0) >= _pageSize),
        suggestions: submitSearch ? [] : state.suggestions,
      ));
      // Save to recent searches
      await _saveRecentSearch(query, emit);
    } catch (e) {
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }

  Future<void> _onLoadMore(Emitter<SearchState> emit) async {
    // Skip if pagination is disabled by config
    if (!enableSearchPagination) return;

    if (state.isLoading || !state.hasMore) return;

    final nextPage = state.page + 1;

    emit(state.copyWith(isLoading: true, page: nextPage));

    try {
      final results = await _searchService.searchProducts(
        query: state.query,
        page: nextPage,
        pageSize: _pageSize,
      );

      emit(state.copyWith(
        isLoading: false,
        products: [...state.products, ...results],
        hasMore: results.length >= _pageSize,
      ));
    } catch (e) {
      emit(state.copyWith(isLoading: false, error: e.toString()));
    }
  }

  Future<void> _onClearSearch(Emitter<SearchState> emit) async {
    try {
      emit(state.copyWith(products: [], suggestions: [], query: ''));
    } catch (_) {}
  }

  Future<void> _onClearRecent(Emitter<SearchState> emit) async {
    try {
      AppPreferences.clearOne(AppPreferences.recentSearches);
      emit(state.copyWith(recentSearches: []));
    } catch (_) {}
  }

  Future<void> _onRemoveRecent(int index, Emitter<SearchState> emit) async {
    final updatedRecents = List<String>.from(state.recentSearches);
    if (index >= 0 && index < updatedRecents.length) {
      updatedRecents.removeAt(index);
      await AppPreferences.setRecentSearches(jsonEncode(updatedRecents));
      emit(state.copyWith(recentSearches: updatedRecents));
    }
  }

  Future<void> _onSelectRecent(String query, Emitter<SearchState> emit) async {
    searchFieldManager?.text = query;
    searchFieldManager?.focusNode.unfocus();
    add(SearchEvent.search(query, submit: true));
  }

  Future<void> _onSelectSuggestion(
      String suggestion, Emitter<SearchState> emit) async {
    searchFieldManager?.text = suggestion;
    searchFieldManager?.focusNode.unfocus();
    add(SearchEvent.search(suggestion, submit: true));
  }

  Future<void> _onInputChange(String query, Emitter<SearchState> emit) async {
    try {
      if (query.length >= 2) {
        _suggestionDebouncer.run(() async {
          add(SearchEvent.updateSuggestions(query));
        });

        _searchDebouncer.run(() {
          if (query == state.query) {
            add(SearchEvent.search(query));
          }
        });
      } else {
        _suggestionDebouncer.run(() {
          add(SearchEvent.clearSearch());
        });
      }
    } catch (_) {}
  }

  Future<void> _onUpdateSuggestions(
      String query, Emitter<SearchState> emit) async {
    try {
      final suggestions = await _searchService.getSearchSuggestions(query);
      emit(state.copyWith(suggestions: suggestions, query: query));
    } catch (_) {}
  }

  Future<void> _saveRecentSearch(
      String query, Emitter<SearchState> emit) async {
    final searches = [...state.recentSearches];
    searches.remove(query);
    searches.insert(0, query);
    if (searches.length > 10) {
      searches.removeRange(10, searches.length);
    }

    await AppPreferences.setRecentSearches(jsonEncode(searches));
    emit(state.copyWith(recentSearches: searches));
  }

  Future<void> _onClearState(Emitter<SearchState> emit) async {
    try {
      emit(state.copyWith(
          suggestions: [],
          products: [],
          categories: [],
          subcategories: [],
          query: ''));
    } catch (_) {}
  }

  @override
  Future<void> close() {
    _suggestionDebouncer.dispose();
    _searchDebouncer.dispose();
    searchFieldManager?.dispose();
    scrollController?.dispose();
    return super.close();
  }
}
