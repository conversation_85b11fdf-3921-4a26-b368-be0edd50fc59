// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SearchEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchEventCopyWith<$Res> {
  factory $SearchEventCopyWith(
          SearchEvent value, $Res Function(SearchEvent) then) =
      _$SearchEventCopyWithImpl<$Res, SearchEvent>;
}

/// @nodoc
class _$SearchEventCopyWithImpl<$Res, $Val extends SearchEvent>
    implements $SearchEventCopyWith<$Res> {
  _$SearchEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitImplCopyWith<$Res> {
  factory _$$InitImplCopyWith(
          _$InitImpl value, $Res Function(_$InitImpl) then) =
      __$$InitImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String initialQuery});
}

/// @nodoc
class __$$InitImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$InitImpl>
    implements _$$InitImplCopyWith<$Res> {
  __$$InitImplCopyWithImpl(_$InitImpl _value, $Res Function(_$InitImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? initialQuery = null,
  }) {
    return _then(_$InitImpl(
      null == initialQuery
          ? _value.initialQuery
          : initialQuery // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$InitImpl implements _Init {
  const _$InitImpl(this.initialQuery);

  @override
  final String initialQuery;

  @override
  String toString() {
    return 'SearchEvent.init(initialQuery: $initialQuery)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InitImpl &&
            (identical(other.initialQuery, initialQuery) ||
                other.initialQuery == initialQuery));
  }

  @override
  int get hashCode => Object.hash(runtimeType, initialQuery);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InitImplCopyWith<_$InitImpl> get copyWith =>
      __$$InitImplCopyWithImpl<_$InitImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return init(initialQuery);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return init?.call(initialQuery);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(initialQuery);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return init(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return init?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(this);
    }
    return orElse();
  }
}

abstract class _Init implements SearchEvent {
  const factory _Init(final String initialQuery) = _$InitImpl;

  String get initialQuery;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InitImplCopyWith<_$InitImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchImplCopyWith<$Res> {
  factory _$$SearchImplCopyWith(
          _$SearchImpl value, $Res Function(_$SearchImpl) then) =
      __$$SearchImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String query, bool submit});
}

/// @nodoc
class __$$SearchImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$SearchImpl>
    implements _$$SearchImplCopyWith<$Res> {
  __$$SearchImplCopyWithImpl(
      _$SearchImpl _value, $Res Function(_$SearchImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
    Object? submit = null,
  }) {
    return _then(_$SearchImpl(
      null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
      submit: null == submit
          ? _value.submit
          : submit // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SearchImpl implements _Search {
  const _$SearchImpl(this.query, {this.submit = false});

  @override
  final String query;
  @override
  @JsonKey()
  final bool submit;

  @override
  String toString() {
    return 'SearchEvent.search(query: $query, submit: $submit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchImpl &&
            (identical(other.query, query) || other.query == query) &&
            (identical(other.submit, submit) || other.submit == submit));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query, submit);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchImplCopyWith<_$SearchImpl> get copyWith =>
      __$$SearchImplCopyWithImpl<_$SearchImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return search(query, submit);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return search?.call(query, submit);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (search != null) {
      return search(query, submit);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return search(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return search?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (search != null) {
      return search(this);
    }
    return orElse();
  }
}

abstract class _Search implements SearchEvent {
  const factory _Search(final String query, {final bool submit}) = _$SearchImpl;

  String get query;
  bool get submit;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchImplCopyWith<_$SearchImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadMoreImplCopyWith<$Res> {
  factory _$$LoadMoreImplCopyWith(
          _$LoadMoreImpl value, $Res Function(_$LoadMoreImpl) then) =
      __$$LoadMoreImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadMoreImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$LoadMoreImpl>
    implements _$$LoadMoreImplCopyWith<$Res> {
  __$$LoadMoreImplCopyWithImpl(
      _$LoadMoreImpl _value, $Res Function(_$LoadMoreImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadMoreImpl implements _LoadMore {
  const _$LoadMoreImpl();

  @override
  String toString() {
    return 'SearchEvent.loadMore()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadMoreImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return loadMore();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return loadMore?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (loadMore != null) {
      return loadMore();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return loadMore(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return loadMore?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (loadMore != null) {
      return loadMore(this);
    }
    return orElse();
  }
}

abstract class _LoadMore implements SearchEvent {
  const factory _LoadMore() = _$LoadMoreImpl;
}

/// @nodoc
abstract class _$$ClearSearchImplCopyWith<$Res> {
  factory _$$ClearSearchImplCopyWith(
          _$ClearSearchImpl value, $Res Function(_$ClearSearchImpl) then) =
      __$$ClearSearchImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearSearchImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$ClearSearchImpl>
    implements _$$ClearSearchImplCopyWith<$Res> {
  __$$ClearSearchImplCopyWithImpl(
      _$ClearSearchImpl _value, $Res Function(_$ClearSearchImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ClearSearchImpl implements _ClearSearch {
  const _$ClearSearchImpl();

  @override
  String toString() {
    return 'SearchEvent.clearSearch()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearSearchImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return clearSearch();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return clearSearch?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (clearSearch != null) {
      return clearSearch();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return clearSearch(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return clearSearch?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (clearSearch != null) {
      return clearSearch(this);
    }
    return orElse();
  }
}

abstract class _ClearSearch implements SearchEvent {
  const factory _ClearSearch() = _$ClearSearchImpl;
}

/// @nodoc
abstract class _$$ClearRecentImplCopyWith<$Res> {
  factory _$$ClearRecentImplCopyWith(
          _$ClearRecentImpl value, $Res Function(_$ClearRecentImpl) then) =
      __$$ClearRecentImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearRecentImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$ClearRecentImpl>
    implements _$$ClearRecentImplCopyWith<$Res> {
  __$$ClearRecentImplCopyWithImpl(
      _$ClearRecentImpl _value, $Res Function(_$ClearRecentImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ClearRecentImpl implements _ClearRecent {
  const _$ClearRecentImpl();

  @override
  String toString() {
    return 'SearchEvent.clearRecent()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearRecentImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return clearRecent();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return clearRecent?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (clearRecent != null) {
      return clearRecent();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return clearRecent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return clearRecent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (clearRecent != null) {
      return clearRecent(this);
    }
    return orElse();
  }
}

abstract class _ClearRecent implements SearchEvent {
  const factory _ClearRecent() = _$ClearRecentImpl;
}

/// @nodoc
abstract class _$$RemoveRecentImplCopyWith<$Res> {
  factory _$$RemoveRecentImplCopyWith(
          _$RemoveRecentImpl value, $Res Function(_$RemoveRecentImpl) then) =
      __$$RemoveRecentImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int index});
}

/// @nodoc
class __$$RemoveRecentImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$RemoveRecentImpl>
    implements _$$RemoveRecentImplCopyWith<$Res> {
  __$$RemoveRecentImplCopyWithImpl(
      _$RemoveRecentImpl _value, $Res Function(_$RemoveRecentImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? index = null,
  }) {
    return _then(_$RemoveRecentImpl(
      null == index
          ? _value.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$RemoveRecentImpl implements _RemoveRecent {
  const _$RemoveRecentImpl(this.index);

  @override
  final int index;

  @override
  String toString() {
    return 'SearchEvent.removeRecent(index: $index)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemoveRecentImpl &&
            (identical(other.index, index) || other.index == index));
  }

  @override
  int get hashCode => Object.hash(runtimeType, index);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RemoveRecentImplCopyWith<_$RemoveRecentImpl> get copyWith =>
      __$$RemoveRecentImplCopyWithImpl<_$RemoveRecentImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return removeRecent(index);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return removeRecent?.call(index);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (removeRecent != null) {
      return removeRecent(index);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return removeRecent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return removeRecent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (removeRecent != null) {
      return removeRecent(this);
    }
    return orElse();
  }
}

abstract class _RemoveRecent implements SearchEvent {
  const factory _RemoveRecent(final int index) = _$RemoveRecentImpl;

  int get index;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RemoveRecentImplCopyWith<_$RemoveRecentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectRecentImplCopyWith<$Res> {
  factory _$$SelectRecentImplCopyWith(
          _$SelectRecentImpl value, $Res Function(_$SelectRecentImpl) then) =
      __$$SelectRecentImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$$SelectRecentImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$SelectRecentImpl>
    implements _$$SelectRecentImplCopyWith<$Res> {
  __$$SelectRecentImplCopyWithImpl(
      _$SelectRecentImpl _value, $Res Function(_$SelectRecentImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
  }) {
    return _then(_$SelectRecentImpl(
      null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SelectRecentImpl implements _SelectRecent {
  const _$SelectRecentImpl(this.query);

  @override
  final String query;

  @override
  String toString() {
    return 'SearchEvent.selectRecent(query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectRecentImpl &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectRecentImplCopyWith<_$SelectRecentImpl> get copyWith =>
      __$$SelectRecentImplCopyWithImpl<_$SelectRecentImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return selectRecent(query);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return selectRecent?.call(query);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (selectRecent != null) {
      return selectRecent(query);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return selectRecent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return selectRecent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (selectRecent != null) {
      return selectRecent(this);
    }
    return orElse();
  }
}

abstract class _SelectRecent implements SearchEvent {
  const factory _SelectRecent(final String query) = _$SelectRecentImpl;

  String get query;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelectRecentImplCopyWith<_$SelectRecentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelectSuggestionImplCopyWith<$Res> {
  factory _$$SelectSuggestionImplCopyWith(_$SelectSuggestionImpl value,
          $Res Function(_$SelectSuggestionImpl) then) =
      __$$SelectSuggestionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String suggestion});
}

/// @nodoc
class __$$SelectSuggestionImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$SelectSuggestionImpl>
    implements _$$SelectSuggestionImplCopyWith<$Res> {
  __$$SelectSuggestionImplCopyWithImpl(_$SelectSuggestionImpl _value,
      $Res Function(_$SelectSuggestionImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? suggestion = null,
  }) {
    return _then(_$SelectSuggestionImpl(
      null == suggestion
          ? _value.suggestion
          : suggestion // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SelectSuggestionImpl implements _SelectSuggestion {
  const _$SelectSuggestionImpl(this.suggestion);

  @override
  final String suggestion;

  @override
  String toString() {
    return 'SearchEvent.selectSuggestion(suggestion: $suggestion)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectSuggestionImpl &&
            (identical(other.suggestion, suggestion) ||
                other.suggestion == suggestion));
  }

  @override
  int get hashCode => Object.hash(runtimeType, suggestion);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectSuggestionImplCopyWith<_$SelectSuggestionImpl> get copyWith =>
      __$$SelectSuggestionImplCopyWithImpl<_$SelectSuggestionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return selectSuggestion(suggestion);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return selectSuggestion?.call(suggestion);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (selectSuggestion != null) {
      return selectSuggestion(suggestion);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return selectSuggestion(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return selectSuggestion?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (selectSuggestion != null) {
      return selectSuggestion(this);
    }
    return orElse();
  }
}

abstract class _SelectSuggestion implements SearchEvent {
  const factory _SelectSuggestion(final String suggestion) =
      _$SelectSuggestionImpl;

  String get suggestion;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelectSuggestionImplCopyWith<_$SelectSuggestionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InputChangeImplCopyWith<$Res> {
  factory _$$InputChangeImplCopyWith(
          _$InputChangeImpl value, $Res Function(_$InputChangeImpl) then) =
      __$$InputChangeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$$InputChangeImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$InputChangeImpl>
    implements _$$InputChangeImplCopyWith<$Res> {
  __$$InputChangeImplCopyWithImpl(
      _$InputChangeImpl _value, $Res Function(_$InputChangeImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
  }) {
    return _then(_$InputChangeImpl(
      null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$InputChangeImpl implements _InputChange {
  const _$InputChangeImpl(this.query);

  @override
  final String query;

  @override
  String toString() {
    return 'SearchEvent.inputChange(query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InputChangeImpl &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InputChangeImplCopyWith<_$InputChangeImpl> get copyWith =>
      __$$InputChangeImplCopyWithImpl<_$InputChangeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return inputChange(query);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return inputChange?.call(query);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (inputChange != null) {
      return inputChange(query);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return inputChange(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return inputChange?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (inputChange != null) {
      return inputChange(this);
    }
    return orElse();
  }
}

abstract class _InputChange implements SearchEvent {
  const factory _InputChange(final String query) = _$InputChangeImpl;

  String get query;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InputChangeImplCopyWith<_$InputChangeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateSuggestionsImplCopyWith<$Res> {
  factory _$$UpdateSuggestionsImplCopyWith(_$UpdateSuggestionsImpl value,
          $Res Function(_$UpdateSuggestionsImpl) then) =
      __$$UpdateSuggestionsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$$UpdateSuggestionsImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$UpdateSuggestionsImpl>
    implements _$$UpdateSuggestionsImplCopyWith<$Res> {
  __$$UpdateSuggestionsImplCopyWithImpl(_$UpdateSuggestionsImpl _value,
      $Res Function(_$UpdateSuggestionsImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
  }) {
    return _then(_$UpdateSuggestionsImpl(
      null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateSuggestionsImpl implements _UpdateSuggestions {
  const _$UpdateSuggestionsImpl(this.query);

  @override
  final String query;

  @override
  String toString() {
    return 'SearchEvent.updateSuggestions(query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateSuggestionsImpl &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateSuggestionsImplCopyWith<_$UpdateSuggestionsImpl> get copyWith =>
      __$$UpdateSuggestionsImplCopyWithImpl<_$UpdateSuggestionsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return updateSuggestions(query);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return updateSuggestions?.call(query);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (updateSuggestions != null) {
      return updateSuggestions(query);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return updateSuggestions(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return updateSuggestions?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (updateSuggestions != null) {
      return updateSuggestions(this);
    }
    return orElse();
  }
}

abstract class _UpdateSuggestions implements SearchEvent {
  const factory _UpdateSuggestions(final String query) =
      _$UpdateSuggestionsImpl;

  String get query;

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateSuggestionsImplCopyWith<_$UpdateSuggestionsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearStateImplCopyWith<$Res> {
  factory _$$ClearStateImplCopyWith(
          _$ClearStateImpl value, $Res Function(_$ClearStateImpl) then) =
      __$$ClearStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearStateImplCopyWithImpl<$Res>
    extends _$SearchEventCopyWithImpl<$Res, _$ClearStateImpl>
    implements _$$ClearStateImplCopyWith<$Res> {
  __$$ClearStateImplCopyWithImpl(
      _$ClearStateImpl _value, $Res Function(_$ClearStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ClearStateImpl implements _ClearState {
  const _$ClearStateImpl();

  @override
  String toString() {
    return 'SearchEvent.clearState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String initialQuery) init,
    required TResult Function(String query, bool submit) search,
    required TResult Function() loadMore,
    required TResult Function() clearSearch,
    required TResult Function() clearRecent,
    required TResult Function(int index) removeRecent,
    required TResult Function(String query) selectRecent,
    required TResult Function(String suggestion) selectSuggestion,
    required TResult Function(String query) inputChange,
    required TResult Function(String query) updateSuggestions,
    required TResult Function() clearState,
  }) {
    return clearState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String initialQuery)? init,
    TResult? Function(String query, bool submit)? search,
    TResult? Function()? loadMore,
    TResult? Function()? clearSearch,
    TResult? Function()? clearRecent,
    TResult? Function(int index)? removeRecent,
    TResult? Function(String query)? selectRecent,
    TResult? Function(String suggestion)? selectSuggestion,
    TResult? Function(String query)? inputChange,
    TResult? Function(String query)? updateSuggestions,
    TResult? Function()? clearState,
  }) {
    return clearState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String initialQuery)? init,
    TResult Function(String query, bool submit)? search,
    TResult Function()? loadMore,
    TResult Function()? clearSearch,
    TResult Function()? clearRecent,
    TResult Function(int index)? removeRecent,
    TResult Function(String query)? selectRecent,
    TResult Function(String suggestion)? selectSuggestion,
    TResult Function(String query)? inputChange,
    TResult Function(String query)? updateSuggestions,
    TResult Function()? clearState,
    required TResult orElse(),
  }) {
    if (clearState != null) {
      return clearState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Search value) search,
    required TResult Function(_LoadMore value) loadMore,
    required TResult Function(_ClearSearch value) clearSearch,
    required TResult Function(_ClearRecent value) clearRecent,
    required TResult Function(_RemoveRecent value) removeRecent,
    required TResult Function(_SelectRecent value) selectRecent,
    required TResult Function(_SelectSuggestion value) selectSuggestion,
    required TResult Function(_InputChange value) inputChange,
    required TResult Function(_UpdateSuggestions value) updateSuggestions,
    required TResult Function(_ClearState value) clearState,
  }) {
    return clearState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Search value)? search,
    TResult? Function(_LoadMore value)? loadMore,
    TResult? Function(_ClearSearch value)? clearSearch,
    TResult? Function(_ClearRecent value)? clearRecent,
    TResult? Function(_RemoveRecent value)? removeRecent,
    TResult? Function(_SelectRecent value)? selectRecent,
    TResult? Function(_SelectSuggestion value)? selectSuggestion,
    TResult? Function(_InputChange value)? inputChange,
    TResult? Function(_UpdateSuggestions value)? updateSuggestions,
    TResult? Function(_ClearState value)? clearState,
  }) {
    return clearState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Search value)? search,
    TResult Function(_LoadMore value)? loadMore,
    TResult Function(_ClearSearch value)? clearSearch,
    TResult Function(_ClearRecent value)? clearRecent,
    TResult Function(_RemoveRecent value)? removeRecent,
    TResult Function(_SelectRecent value)? selectRecent,
    TResult Function(_SelectSuggestion value)? selectSuggestion,
    TResult Function(_InputChange value)? inputChange,
    TResult Function(_UpdateSuggestions value)? updateSuggestions,
    TResult Function(_ClearState value)? clearState,
    required TResult orElse(),
  }) {
    if (clearState != null) {
      return clearState(this);
    }
    return orElse();
  }
}

abstract class _ClearState implements SearchEvent {
  const factory _ClearState() = _$ClearStateImpl;
}
