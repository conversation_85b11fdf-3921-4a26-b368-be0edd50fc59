// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LoginState {
  String get mobile => throw _privateConstructorUsedError;
  bool get showError => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String mobile, bool showError, bool isLoading)
        initial,
    required TResult Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)
        otp,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String mobile, bool showError, bool isLoading)? initial,
    TResult? Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)?
        otp,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String mobile, bool showError, bool isLoading)? initial,
    TResult Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)?
        otp,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoginInitial value) initial,
    required TResult Function(_LoginOTP value) otp,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoginInitial value)? initial,
    TResult? Function(_LoginOTP value)? otp,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoginInitial value)? initial,
    TResult Function(_LoginOTP value)? otp,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoginStateCopyWith<LoginState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginStateCopyWith<$Res> {
  factory $LoginStateCopyWith(
          LoginState value, $Res Function(LoginState) then) =
      _$LoginStateCopyWithImpl<$Res, LoginState>;
  @useResult
  $Res call({String mobile, bool showError, bool isLoading});
}

/// @nodoc
class _$LoginStateCopyWithImpl<$Res, $Val extends LoginState>
    implements $LoginStateCopyWith<$Res> {
  _$LoginStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = null,
    Object? showError = null,
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      showError: null == showError
          ? _value.showError
          : showError // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoginInitialImplCopyWith<$Res>
    implements $LoginStateCopyWith<$Res> {
  factory _$$LoginInitialImplCopyWith(
          _$LoginInitialImpl value, $Res Function(_$LoginInitialImpl) then) =
      __$$LoginInitialImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String mobile, bool showError, bool isLoading});
}

/// @nodoc
class __$$LoginInitialImplCopyWithImpl<$Res>
    extends _$LoginStateCopyWithImpl<$Res, _$LoginInitialImpl>
    implements _$$LoginInitialImplCopyWith<$Res> {
  __$$LoginInitialImplCopyWithImpl(
      _$LoginInitialImpl _value, $Res Function(_$LoginInitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = null,
    Object? showError = null,
    Object? isLoading = null,
  }) {
    return _then(_$LoginInitialImpl(
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      showError: null == showError
          ? _value.showError
          : showError // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$LoginInitialImpl implements _LoginInitial {
  const _$LoginInitialImpl(
      {this.mobile = '', this.showError = false, this.isLoading = false});

  @override
  @JsonKey()
  final String mobile;
  @override
  @JsonKey()
  final bool showError;
  @override
  @JsonKey()
  final bool isLoading;

  @override
  String toString() {
    return 'LoginState.initial(mobile: $mobile, showError: $showError, isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginInitialImpl &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.showError, showError) ||
                other.showError == showError) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mobile, showError, isLoading);

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginInitialImplCopyWith<_$LoginInitialImpl> get copyWith =>
      __$$LoginInitialImplCopyWithImpl<_$LoginInitialImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String mobile, bool showError, bool isLoading)
        initial,
    required TResult Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)
        otp,
  }) {
    return initial(mobile, showError, isLoading);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String mobile, bool showError, bool isLoading)? initial,
    TResult? Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)?
        otp,
  }) {
    return initial?.call(mobile, showError, isLoading);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String mobile, bool showError, bool isLoading)? initial,
    TResult Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)?
        otp,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(mobile, showError, isLoading);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoginInitial value) initial,
    required TResult Function(_LoginOTP value) otp,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoginInitial value)? initial,
    TResult? Function(_LoginOTP value)? otp,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoginInitial value)? initial,
    TResult Function(_LoginOTP value)? otp,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _LoginInitial implements LoginState {
  const factory _LoginInitial(
      {final String mobile,
      final bool showError,
      final bool isLoading}) = _$LoginInitialImpl;

  @override
  String get mobile;
  @override
  bool get showError;
  @override
  bool get isLoading;

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginInitialImplCopyWith<_$LoginInitialImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoginOTPImplCopyWith<$Res>
    implements $LoginStateCopyWith<$Res> {
  factory _$$LoginOTPImplCopyWith(
          _$LoginOTPImpl value, $Res Function(_$LoginOTPImpl) then) =
      __$$LoginOTPImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String mobile,
      String otp,
      String verificationId,
      bool showError,
      bool isLoading,
      bool canResend,
      int resendSeconds});
}

/// @nodoc
class __$$LoginOTPImplCopyWithImpl<$Res>
    extends _$LoginStateCopyWithImpl<$Res, _$LoginOTPImpl>
    implements _$$LoginOTPImplCopyWith<$Res> {
  __$$LoginOTPImplCopyWithImpl(
      _$LoginOTPImpl _value, $Res Function(_$LoginOTPImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mobile = null,
    Object? otp = null,
    Object? verificationId = null,
    Object? showError = null,
    Object? isLoading = null,
    Object? canResend = null,
    Object? resendSeconds = null,
  }) {
    return _then(_$LoginOTPImpl(
      mobile: null == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String,
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
      verificationId: null == verificationId
          ? _value.verificationId
          : verificationId // ignore: cast_nullable_to_non_nullable
              as String,
      showError: null == showError
          ? _value.showError
          : showError // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      canResend: null == canResend
          ? _value.canResend
          : canResend // ignore: cast_nullable_to_non_nullable
              as bool,
      resendSeconds: null == resendSeconds
          ? _value.resendSeconds
          : resendSeconds // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$LoginOTPImpl implements _LoginOTP {
  const _$LoginOTPImpl(
      {this.mobile = '',
      this.otp = '',
      this.verificationId = '',
      this.showError = false,
      this.isLoading = false,
      this.canResend = false,
      this.resendSeconds = 30});

  @override
  @JsonKey()
  final String mobile;
  @override
  @JsonKey()
  final String otp;
  @override
  @JsonKey()
  final String verificationId;
  @override
  @JsonKey()
  final bool showError;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool canResend;
  @override
  @JsonKey()
  final int resendSeconds;

  @override
  String toString() {
    return 'LoginState.otp(mobile: $mobile, otp: $otp, verificationId: $verificationId, showError: $showError, isLoading: $isLoading, canResend: $canResend, resendSeconds: $resendSeconds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginOTPImpl &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            (identical(other.otp, otp) || other.otp == otp) &&
            (identical(other.verificationId, verificationId) ||
                other.verificationId == verificationId) &&
            (identical(other.showError, showError) ||
                other.showError == showError) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.canResend, canResend) ||
                other.canResend == canResend) &&
            (identical(other.resendSeconds, resendSeconds) ||
                other.resendSeconds == resendSeconds));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mobile, otp, verificationId,
      showError, isLoading, canResend, resendSeconds);

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginOTPImplCopyWith<_$LoginOTPImpl> get copyWith =>
      __$$LoginOTPImplCopyWithImpl<_$LoginOTPImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String mobile, bool showError, bool isLoading)
        initial,
    required TResult Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)
        otp,
  }) {
    return otp(mobile, this.otp, verificationId, showError, isLoading,
        canResend, resendSeconds);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String mobile, bool showError, bool isLoading)? initial,
    TResult? Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)?
        otp,
  }) {
    return otp?.call(mobile, this.otp, verificationId, showError, isLoading,
        canResend, resendSeconds);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String mobile, bool showError, bool isLoading)? initial,
    TResult Function(String mobile, String otp, String verificationId,
            bool showError, bool isLoading, bool canResend, int resendSeconds)?
        otp,
    required TResult orElse(),
  }) {
    if (otp != null) {
      return otp(mobile, this.otp, verificationId, showError, isLoading,
          canResend, resendSeconds);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoginInitial value) initial,
    required TResult Function(_LoginOTP value) otp,
  }) {
    return otp(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoginInitial value)? initial,
    TResult? Function(_LoginOTP value)? otp,
  }) {
    return otp?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoginInitial value)? initial,
    TResult Function(_LoginOTP value)? otp,
    required TResult orElse(),
  }) {
    if (otp != null) {
      return otp(this);
    }
    return orElse();
  }
}

abstract class _LoginOTP implements LoginState {
  const factory _LoginOTP(
      {final String mobile,
      final String otp,
      final String verificationId,
      final bool showError,
      final bool isLoading,
      final bool canResend,
      final int resendSeconds}) = _$LoginOTPImpl;

  @override
  String get mobile;
  String get otp;
  String get verificationId;
  @override
  bool get showError;
  @override
  bool get isLoading;
  bool get canResend;
  int get resendSeconds;

  /// Create a copy of LoginState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginOTPImplCopyWith<_$LoginOTPImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
