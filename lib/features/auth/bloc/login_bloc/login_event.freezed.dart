// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LoginEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginEventCopyWith<$Res> {
  factory $LoginEventCopyWith(
          LoginEvent value, $Res Function(LoginEvent) then) =
      _$LoginEventCopyWithImpl<$Res, LoginEvent>;
}

/// @nodoc
class _$LoginEventCopyWithImpl<$Res, $Val extends LoginEvent>
    implements $LoginEventCopyWith<$Res> {
  _$LoginEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitLoginImplCopyWith<$Res> {
  factory _$$InitLoginImplCopyWith(
          _$InitLoginImpl value, $Res Function(_$InitLoginImpl) then) =
      __$$InitLoginImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitLoginImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$InitLoginImpl>
    implements _$$InitLoginImplCopyWith<$Res> {
  __$$InitLoginImplCopyWithImpl(
      _$InitLoginImpl _value, $Res Function(_$InitLoginImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitLoginImpl implements _InitLogin {
  const _$InitLoginImpl();

  @override
  String toString() {
    return 'LoginEvent.initLogin()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitLoginImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return initLogin();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return initLogin?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (initLogin != null) {
      return initLogin();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return initLogin(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return initLogin?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (initLogin != null) {
      return initLogin(this);
    }
    return orElse();
  }
}

abstract class _InitLogin implements LoginEvent {
  const factory _InitLogin() = _$InitLoginImpl;
}

/// @nodoc
abstract class _$$MobileChangedImplCopyWith<$Res> {
  factory _$$MobileChangedImplCopyWith(
          _$MobileChangedImpl value, $Res Function(_$MobileChangedImpl) then) =
      __$$MobileChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$MobileChangedImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$MobileChangedImpl>
    implements _$$MobileChangedImplCopyWith<$Res> {
  __$$MobileChangedImplCopyWithImpl(
      _$MobileChangedImpl _value, $Res Function(_$MobileChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$MobileChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$MobileChangedImpl implements _MobileChanged {
  const _$MobileChangedImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'LoginEvent.mobileChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MobileChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MobileChangedImplCopyWith<_$MobileChangedImpl> get copyWith =>
      __$$MobileChangedImplCopyWithImpl<_$MobileChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return mobileChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return mobileChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (mobileChanged != null) {
      return mobileChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return mobileChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return mobileChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (mobileChanged != null) {
      return mobileChanged(this);
    }
    return orElse();
  }
}

abstract class _MobileChanged implements LoginEvent {
  const factory _MobileChanged(final String value) = _$MobileChangedImpl;

  String get value;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MobileChangedImplCopyWith<_$MobileChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubmitLoginImplCopyWith<$Res> {
  factory _$$SubmitLoginImplCopyWith(
          _$SubmitLoginImpl value, $Res Function(_$SubmitLoginImpl) then) =
      __$$SubmitLoginImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SubmitLoginImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$SubmitLoginImpl>
    implements _$$SubmitLoginImplCopyWith<$Res> {
  __$$SubmitLoginImplCopyWithImpl(
      _$SubmitLoginImpl _value, $Res Function(_$SubmitLoginImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SubmitLoginImpl implements _SubmitLogin {
  const _$SubmitLoginImpl();

  @override
  String toString() {
    return 'LoginEvent.submitLogin()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SubmitLoginImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return submitLogin();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return submitLogin?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (submitLogin != null) {
      return submitLogin();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return submitLogin(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return submitLogin?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (submitLogin != null) {
      return submitLogin(this);
    }
    return orElse();
  }
}

abstract class _SubmitLogin implements LoginEvent {
  const factory _SubmitLogin() = _$SubmitLoginImpl;
}

/// @nodoc
abstract class _$$FailedLoginImplCopyWith<$Res> {
  factory _$$FailedLoginImplCopyWith(
          _$FailedLoginImpl value, $Res Function(_$FailedLoginImpl) then) =
      __$$FailedLoginImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FailedLoginImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$FailedLoginImpl>
    implements _$$FailedLoginImplCopyWith<$Res> {
  __$$FailedLoginImplCopyWithImpl(
      _$FailedLoginImpl _value, $Res Function(_$FailedLoginImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FailedLoginImpl implements _FailedLogin {
  const _$FailedLoginImpl();

  @override
  String toString() {
    return 'LoginEvent.loginFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FailedLoginImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return loginFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return loginFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (loginFailed != null) {
      return loginFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return loginFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return loginFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (loginFailed != null) {
      return loginFailed(this);
    }
    return orElse();
  }
}

abstract class _FailedLogin implements LoginEvent {
  const factory _FailedLogin() = _$FailedLoginImpl;
}

/// @nodoc
abstract class _$$InitOTPImplCopyWith<$Res> {
  factory _$$InitOTPImplCopyWith(
          _$InitOTPImpl value, $Res Function(_$InitOTPImpl) then) =
      __$$InitOTPImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String verificationId, String mobileNumber});
}

/// @nodoc
class __$$InitOTPImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$InitOTPImpl>
    implements _$$InitOTPImplCopyWith<$Res> {
  __$$InitOTPImplCopyWithImpl(
      _$InitOTPImpl _value, $Res Function(_$InitOTPImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? verificationId = null,
    Object? mobileNumber = null,
  }) {
    return _then(_$InitOTPImpl(
      null == verificationId
          ? _value.verificationId
          : verificationId // ignore: cast_nullable_to_non_nullable
              as String,
      null == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$InitOTPImpl implements _InitOTP {
  const _$InitOTPImpl(this.verificationId, this.mobileNumber);

  @override
  final String verificationId;
  @override
  final String mobileNumber;

  @override
  String toString() {
    return 'LoginEvent.initOTP(verificationId: $verificationId, mobileNumber: $mobileNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InitOTPImpl &&
            (identical(other.verificationId, verificationId) ||
                other.verificationId == verificationId) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, verificationId, mobileNumber);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InitOTPImplCopyWith<_$InitOTPImpl> get copyWith =>
      __$$InitOTPImplCopyWithImpl<_$InitOTPImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return initOTP(verificationId, mobileNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return initOTP?.call(verificationId, mobileNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (initOTP != null) {
      return initOTP(verificationId, mobileNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return initOTP(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return initOTP?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (initOTP != null) {
      return initOTP(this);
    }
    return orElse();
  }
}

abstract class _InitOTP implements LoginEvent {
  const factory _InitOTP(
      final String verificationId, final String mobileNumber) = _$InitOTPImpl;

  String get verificationId;
  String get mobileNumber;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InitOTPImplCopyWith<_$InitOTPImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OTPTimerImplCopyWith<$Res> {
  factory _$$OTPTimerImplCopyWith(
          _$OTPTimerImpl value, $Res Function(_$OTPTimerImpl) then) =
      __$$OTPTimerImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int second});
}

/// @nodoc
class __$$OTPTimerImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$OTPTimerImpl>
    implements _$$OTPTimerImplCopyWith<$Res> {
  __$$OTPTimerImplCopyWithImpl(
      _$OTPTimerImpl _value, $Res Function(_$OTPTimerImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? second = null,
  }) {
    return _then(_$OTPTimerImpl(
      null == second
          ? _value.second
          : second // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$OTPTimerImpl implements _OTPTimer {
  const _$OTPTimerImpl(this.second);

  @override
  final int second;

  @override
  String toString() {
    return 'LoginEvent.updateTimer(second: $second)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OTPTimerImpl &&
            (identical(other.second, second) || other.second == second));
  }

  @override
  int get hashCode => Object.hash(runtimeType, second);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OTPTimerImplCopyWith<_$OTPTimerImpl> get copyWith =>
      __$$OTPTimerImplCopyWithImpl<_$OTPTimerImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return updateTimer(second);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return updateTimer?.call(second);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (updateTimer != null) {
      return updateTimer(second);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return updateTimer(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return updateTimer?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (updateTimer != null) {
      return updateTimer(this);
    }
    return orElse();
  }
}

abstract class _OTPTimer implements LoginEvent {
  const factory _OTPTimer(final int second) = _$OTPTimerImpl;

  int get second;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OTPTimerImplCopyWith<_$OTPTimerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OTPResendImplCopyWith<$Res> {
  factory _$$OTPResendImplCopyWith(
          _$OTPResendImpl value, $Res Function(_$OTPResendImpl) then) =
      __$$OTPResendImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OTPResendImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$OTPResendImpl>
    implements _$$OTPResendImplCopyWith<$Res> {
  __$$OTPResendImplCopyWithImpl(
      _$OTPResendImpl _value, $Res Function(_$OTPResendImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OTPResendImpl implements _OTPResend {
  const _$OTPResendImpl();

  @override
  String toString() {
    return 'LoginEvent.resendOTP()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$OTPResendImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return resendOTP();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return resendOTP?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (resendOTP != null) {
      return resendOTP();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return resendOTP(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return resendOTP?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (resendOTP != null) {
      return resendOTP(this);
    }
    return orElse();
  }
}

abstract class _OTPResend implements LoginEvent {
  const factory _OTPResend() = _$OTPResendImpl;
}

/// @nodoc
abstract class _$$OtpChangedImplCopyWith<$Res> {
  factory _$$OtpChangedImplCopyWith(
          _$OtpChangedImpl value, $Res Function(_$OtpChangedImpl) then) =
      __$$OtpChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$OtpChangedImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$OtpChangedImpl>
    implements _$$OtpChangedImplCopyWith<$Res> {
  __$$OtpChangedImplCopyWithImpl(
      _$OtpChangedImpl _value, $Res Function(_$OtpChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$OtpChangedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OtpChangedImpl implements _OtpChanged {
  const _$OtpChangedImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'LoginEvent.otpChanged(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtpChangedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtpChangedImplCopyWith<_$OtpChangedImpl> get copyWith =>
      __$$OtpChangedImplCopyWithImpl<_$OtpChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return otpChanged(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return otpChanged?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (otpChanged != null) {
      return otpChanged(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return otpChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return otpChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (otpChanged != null) {
      return otpChanged(this);
    }
    return orElse();
  }
}

abstract class _OtpChanged implements LoginEvent {
  const factory _OtpChanged(final String value) = _$OtpChangedImpl;

  String get value;

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtpChangedImplCopyWith<_$OtpChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubmitOTPImplCopyWith<$Res> {
  factory _$$SubmitOTPImplCopyWith(
          _$SubmitOTPImpl value, $Res Function(_$SubmitOTPImpl) then) =
      __$$SubmitOTPImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SubmitOTPImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$SubmitOTPImpl>
    implements _$$SubmitOTPImplCopyWith<$Res> {
  __$$SubmitOTPImplCopyWithImpl(
      _$SubmitOTPImpl _value, $Res Function(_$SubmitOTPImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SubmitOTPImpl implements _SubmitOTP {
  const _$SubmitOTPImpl();

  @override
  String toString() {
    return 'LoginEvent.submitOTP()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SubmitOTPImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return submitOTP();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return submitOTP?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (submitOTP != null) {
      return submitOTP();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return submitOTP(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return submitOTP?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (submitOTP != null) {
      return submitOTP(this);
    }
    return orElse();
  }
}

abstract class _SubmitOTP implements LoginEvent {
  const factory _SubmitOTP() = _$SubmitOTPImpl;
}

/// @nodoc
abstract class _$$FailedOTPImplCopyWith<$Res> {
  factory _$$FailedOTPImplCopyWith(
          _$FailedOTPImpl value, $Res Function(_$FailedOTPImpl) then) =
      __$$FailedOTPImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FailedOTPImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$FailedOTPImpl>
    implements _$$FailedOTPImplCopyWith<$Res> {
  __$$FailedOTPImplCopyWithImpl(
      _$FailedOTPImpl _value, $Res Function(_$FailedOTPImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FailedOTPImpl implements _FailedOTP {
  const _$FailedOTPImpl();

  @override
  String toString() {
    return 'LoginEvent.otpFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FailedOTPImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return otpFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return otpFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (otpFailed != null) {
      return otpFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return otpFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return otpFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (otpFailed != null) {
      return otpFailed(this);
    }
    return orElse();
  }
}

abstract class _FailedOTP implements LoginEvent {
  const factory _FailedOTP() = _$FailedOTPImpl;
}

/// @nodoc
abstract class _$$GoogleSigninImplCopyWith<$Res> {
  factory _$$GoogleSigninImplCopyWith(
          _$GoogleSigninImpl value, $Res Function(_$GoogleSigninImpl) then) =
      __$$GoogleSigninImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GoogleSigninImplCopyWithImpl<$Res>
    extends _$LoginEventCopyWithImpl<$Res, _$GoogleSigninImpl>
    implements _$$GoogleSigninImplCopyWith<$Res> {
  __$$GoogleSigninImplCopyWithImpl(
      _$GoogleSigninImpl _value, $Res Function(_$GoogleSigninImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GoogleSigninImpl implements _GoogleSignin {
  const _$GoogleSigninImpl();

  @override
  String toString() {
    return 'LoginEvent.googleSignin()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GoogleSigninImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLogin,
    required TResult Function(String value) mobileChanged,
    required TResult Function() submitLogin,
    required TResult Function() loginFailed,
    required TResult Function(String verificationId, String mobileNumber)
        initOTP,
    required TResult Function(int second) updateTimer,
    required TResult Function() resendOTP,
    required TResult Function(String value) otpChanged,
    required TResult Function() submitOTP,
    required TResult Function() otpFailed,
    required TResult Function() googleSignin,
  }) {
    return googleSignin();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLogin,
    TResult? Function(String value)? mobileChanged,
    TResult? Function()? submitLogin,
    TResult? Function()? loginFailed,
    TResult? Function(String verificationId, String mobileNumber)? initOTP,
    TResult? Function(int second)? updateTimer,
    TResult? Function()? resendOTP,
    TResult? Function(String value)? otpChanged,
    TResult? Function()? submitOTP,
    TResult? Function()? otpFailed,
    TResult? Function()? googleSignin,
  }) {
    return googleSignin?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLogin,
    TResult Function(String value)? mobileChanged,
    TResult Function()? submitLogin,
    TResult Function()? loginFailed,
    TResult Function(String verificationId, String mobileNumber)? initOTP,
    TResult Function(int second)? updateTimer,
    TResult Function()? resendOTP,
    TResult Function(String value)? otpChanged,
    TResult Function()? submitOTP,
    TResult Function()? otpFailed,
    TResult Function()? googleSignin,
    required TResult orElse(),
  }) {
    if (googleSignin != null) {
      return googleSignin();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLogin value) initLogin,
    required TResult Function(_MobileChanged value) mobileChanged,
    required TResult Function(_SubmitLogin value) submitLogin,
    required TResult Function(_FailedLogin value) loginFailed,
    required TResult Function(_InitOTP value) initOTP,
    required TResult Function(_OTPTimer value) updateTimer,
    required TResult Function(_OTPResend value) resendOTP,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_SubmitOTP value) submitOTP,
    required TResult Function(_FailedOTP value) otpFailed,
    required TResult Function(_GoogleSignin value) googleSignin,
  }) {
    return googleSignin(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLogin value)? initLogin,
    TResult? Function(_MobileChanged value)? mobileChanged,
    TResult? Function(_SubmitLogin value)? submitLogin,
    TResult? Function(_FailedLogin value)? loginFailed,
    TResult? Function(_InitOTP value)? initOTP,
    TResult? Function(_OTPTimer value)? updateTimer,
    TResult? Function(_OTPResend value)? resendOTP,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_SubmitOTP value)? submitOTP,
    TResult? Function(_FailedOTP value)? otpFailed,
    TResult? Function(_GoogleSignin value)? googleSignin,
  }) {
    return googleSignin?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLogin value)? initLogin,
    TResult Function(_MobileChanged value)? mobileChanged,
    TResult Function(_SubmitLogin value)? submitLogin,
    TResult Function(_FailedLogin value)? loginFailed,
    TResult Function(_InitOTP value)? initOTP,
    TResult Function(_OTPTimer value)? updateTimer,
    TResult Function(_OTPResend value)? resendOTP,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_SubmitOTP value)? submitOTP,
    TResult Function(_FailedOTP value)? otpFailed,
    TResult Function(_GoogleSignin value)? googleSignin,
    required TResult orElse(),
  }) {
    if (googleSignin != null) {
      return googleSignin(this);
    }
    return orElse();
  }
}

abstract class _GoogleSignin implements LoginEvent {
  const factory _GoogleSignin() = _$GoogleSigninImpl;
}
