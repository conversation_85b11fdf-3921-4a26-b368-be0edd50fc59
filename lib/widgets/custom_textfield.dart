import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../core/themes/color_schemes.dart';
import 'custom_text.dart';
import 'shimmer_widgets.dart';

class CustomInputBorder extends OutlineInputBorder {
  const CustomInputBorder({
    super.borderSide,
    this.radius,
  });

  final double? radius;

  @override
  BorderSide get borderSide => const BorderSide(color: AppColors.textHint);

  @override
  BorderRadius get borderRadius =>
      BorderRadius.all(Radius.circular(radius ?? 6));
}

class TransparentInputBorder extends OutlineInputBorder {
  const TransparentInputBorder({
    super.borderSide = const BorderSide(color: Colors.transparent),
    this.radius,
  });

  final double? radius;

  @override
  BorderRadius get borderRadius =>
      BorderRadius.all(Radius.circular(radius ?? 100));
}

class CustomTextField extends TextField {
  const CustomTextField({
    super.key,
    super.controller,
    super.focusNode,
    super.undoController,
    super.decoration,
    super.keyboardType = TextInputType.text,
    super.textInputAction = TextInputAction.next,
    super.textCapitalization = TextCapitalization.sentences,
    super.style,
    super.strutStyle,
    super.textAlign = TextAlign.start,
    super.textAlignVertical,
    super.textDirection,
    super.readOnly = false,
    super.toolbarOptions,
    super.showCursor,
    super.autofocus = false,
    super.obscuringCharacter = '•',
    super.obscureText = false,
    super.autocorrect = true,
    super.enableSuggestions = true,
    super.maxLines = 1,
    super.minLines,
    super.expands = false,
    super.maxLength,
    super.maxLengthEnforcement,
    super.onChanged,
    super.onEditingComplete,
    super.onSubmitted,
    super.onAppPrivateCommand,
    super.inputFormatters,
    super.enabled,
    super.cursorWidth = 2.0,
    super.cursorHeight,
    super.cursorRadius,
    super.cursorOpacityAnimates,
    super.cursorColor = AppColors.primary,
    super.keyboardAppearance,
    super.scrollPadding = const EdgeInsets.only(bottom: 200),
    super.dragStartBehavior = DragStartBehavior.start,
    bool? enableInteractiveSelection,
    super.selectionControls,
    super.onTap,
    super.onTapOutside,
    super.mouseCursor,
    super.buildCounter,
    super.scrollController,
    super.scrollPhysics,
    super.autofillHints = const <String>[],
    super.canRequestFocus = true,
    this.isMandatory = false,
    this.hasLabel = false,
    this.hintText,
    this.borderRadius = 10,
    this.padding,
  });

  final bool isMandatory;
  final bool hasLabel;
  final String? hintText;
  final double? borderRadius;
  final EdgeInsets? padding;

  @override
  TapRegionCallback? get onTapOutside => (event) {
        focusNode?.unfocus();
      };

  @override
  TextStyle? get style => (super.enabled ?? true)
      ? const TextStyle(
          fontSize: 14,
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w500,
          height: 1.4,
          overflow: TextOverflow.ellipsis,
        )
      : null;

  @override
  InputDecoration? get decoration => super.decoration?.copyWith(
        hintText: hintText,
        contentPadding: padding ?? const EdgeInsets.fromLTRB(16, 20, 16, 10),
        border: CustomInputBorder(radius: borderRadius),
        enabledBorder: CustomInputBorder(radius: borderRadius),
        disabledBorder: CustomInputBorder(radius: borderRadius),
        focusedBorder: CustomInputBorder(radius: borderRadius),
        labelText: hasLabel && isMandatory ? '*' : null,
        filled: true,
        fillColor: super.decoration?.fillColor ?? AppColors.background,
        floatingLabelStyle: const TextStyle(
            color: AppColors.primary,
            fontSize: 16,
            fontWeight: FontWeight.w500),
        labelStyle: const TextStyle(
            color: AppColors.primary,
            fontSize: 14,
            fontWeight: FontWeight.w500),
        hintStyle: const TextStyle(
          fontSize: 14,
          color: AppColors.textGrey,
          // fontWeight: FontWeight.w500,
          overflow: TextOverflow.ellipsis,
        ),
        helperStyle: const TextStyle(
            // color: Get.theme.primaryBlue,
            fontSize: 12,
            fontWeight: FontWeight.w400,
            overflow: TextOverflow.visible),
        helperMaxLines: 5,
        counter: const SizedBox(),
      );
}

class SearchTextField extends CustomTextField {
  const SearchTextField({
    super.key,
    super.controller,
    super.focusNode,
    super.keyboardType = TextInputType.text,
    super.textInputAction = TextInputAction.search,
    super.textCapitalization = TextCapitalization.sentences,
    super.decoration,
    super.isMandatory = false,
    super.hasLabel = false,
    super.hintText = 'Search',
    super.readOnly = false,
    super.enabled = true,
    super.onChanged,
    super.onSubmitted,
    super.autofocus = true,
    super.borderRadius = 8,
    this.onClear,
    this.showBorder = false,
    super.onTapOutside,
  });

  final VoidCallback? onClear;
  final bool showBorder;

  @override
  InputDecoration? get decoration => super.decoration?.copyWith(
        hintText: hintText ?? 'Search for fruits, vegetables, groceries...',
        fillColor: Colors.grey[100],
        contentPadding: super.decoration?.contentPadding ??
            EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        prefixIcon: const Icon(
          Icons.search,
          color: AppColors.textSecondary,
          size: 20,
        ),
        prefixIconConstraints: BoxConstraints(minWidth: 36, minHeight: 36),
        suffixIcon: controller != null && controller!.text.isNotEmpty
            ? IconButton(
                icon: Icon(
                  Icons.clear,
                  color: AppColors.textSecondary,
                  size: 18,
                ),
                onPressed: () {
                  controller!.clear();
                  if (onClear != null) {
                    onClear!();
                  } else if (onChanged != null) {
                    onChanged!('');
                  }
                },
                constraints: BoxConstraints(minWidth: 36, minHeight: 36),
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 8),
          borderSide: showBorder
              ? BorderSide(color: Colors.grey.shade200)
              : BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 8),
          borderSide: showBorder
              ? BorderSide(color: Colors.grey.shade200)
              : BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 8),
          borderSide: BorderSide(
            color: AppColors.primary.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
      );
}

class Titledfield extends StatelessWidget {
  const Titledfield({
    super.key,
    required this.title,
    required this.field,
    this.hasBottomSpacing = true,
    this.isLoading = false,
    this.titleWidget,
    this.bottomSpacing,
    this.fontSize,
    this.opacity = 1,
    this.errorText,
  });

  final String title;
  final Widget? titleWidget;
  final Widget field;
  final bool hasBottomSpacing;
  final double? bottomSpacing;
  final bool isLoading;
  final double? fontSize;
  final double opacity;
  final String? errorText;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        titleWidget ??
            MainHeaderText(
              title,
              fontSize: 14,
              color: AppColors.textPrimary,
            ),
        const SizedBox(height: 6),
        isLoading
            ? const ShimmerTextField()
            : Opacity(opacity: opacity, child: field),
        Visibility(
          visible: errorText?.isNotEmpty ?? false,
          child: Padding(
            padding: const EdgeInsets.only(top: 8, left: 10),
            child: CustomText(
              errorText ?? '',
              color: AppColors.secondaryDark,
              overflow: TextOverflow.visible,
            ),
          ),
        ),
        SizedBox(height: hasBottomSpacing ? (bottomSpacing ?? 24) : 0),
      ],
    );
  }
}

class FieldErrorText extends StatelessWidget {
  const FieldErrorText({super.key, required this.errorText});
  final String errorText;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8, left: 10),
      child: CustomText(
        errorText,
        color: AppColors.secondaryDark,
      ),
    );
  }
}
