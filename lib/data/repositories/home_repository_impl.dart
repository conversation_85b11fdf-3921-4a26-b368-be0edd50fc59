import 'package:rozana/data/services/data_loading_manager.dart';
import 'package:rozana/domain/repositories/home_repository_interface.dart';
import 'package:rozana/domain/entities/banner_entity.dart';
import 'package:rozana/domain/entities/category_entity.dart';
import 'package:rozana/domain/entities/product_entity.dart';

import '../models/banner_model.dart';
import '../models/category_model.dart';
import '../models/product_model.dart';
import '../mappers/banner_mapper.dart';
import '../mappers/category_mapper.dart';
import '../mappers/product_mapper.dart';

class HomeRepositoryImpl implements IHomeRepository {
  final DataLoadingManager _dataManager;

  // Constructor dependency injection
  HomeRepositoryImpl({required DataLoadingManager dataManager})
      : _dataManager = dataManager;

  @override
  Future<List<CategoryEntity>> getCategories({
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
  }) async {
    List<Map<String, dynamic>> response = await _dataManager.loadCategories(
        page: page, pageSize: pageSize, refresh: refresh, query: query);

    List<CategoryModel> categories =
        response.map((json) => CategoryModel.fromJson(json)).toList();
    return CategoryMapper.toEntityList(categories);
  }

  @override
  Future<List<ProductEntity>> getProducts({
    int page = 0,
    int pageSize = 9,
    bool refresh = false,
    String query = '',
  }) async {
    List<Map<String, dynamic>> response =
        await _dataManager.loadFeaturedProducts(
            page: page, pageSize: pageSize, refresh: refresh, query: query);

    List<ProductModel> products =
        response.map((json) => ProductModel.fromJson(json)).toList();
    return ProductMapper.toEntityList(products);
  }

  @override
  Future<List<BannerEntity>> getBanners() async {
    List<Map<String, dynamic>> response = await _dataManager.getBanners();

    List<HomeBanner> banners =
        response.map((json) => HomeBanner.fromJson(json)).toList();
    return BannerMapper.toEntityList(banners);
  }
}

Future fetchDummy(dynamic model) =>
    Future.delayed(Duration(seconds: 1), () => model);
