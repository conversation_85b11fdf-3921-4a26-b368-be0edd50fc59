import 'package:dio/dio.dart';
import 'package:rozana/core/network/api_client.dart';
import 'package:rozana/core/network/api_endpoints.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import 'package:rozana/data/services/data_loading_manager.dart';
import 'package:rozana/data/models/order_model.dart';
import 'package:rozana/data/mappers/order_mapper.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';

class OrderRepositoryImpl implements OrderRepositoryInterface {
  @override
  Future<Response?> createOrder({
    required String customerId,
    required String customerName,
    required String facilityId,
    required String facilityName,
    required num totalAmount,
    required List<Map<String, dynamic>> items,
  }) async {
    final orderPayload = {
      'customer_id': customerId,
      'customer_name': customerName,
      'facility_id': facilityId,
      'facility_name': facilityName,
      'status': 'pending',
      'total_amount': totalAmount,
      'items': items
    };

    return await ApiClient.sendHttpRequest(
      endUrl: EndUrl.createOrder,
      method: HttpMethod.post,
      data: orderPayload,
    );
  }

  @override
  Future<List<OrderEntity>> getOrderHistory({
    String customerId = 'customer_123',
    String status = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
  }) async {
    try {
      // Make API call to get real orders
      final response = await ApiClient.sendHttpRequest(
        endUrl: '${EndUrl.getOrderDetails}?customer_id=$customerId&status=$status&page=$page&limit=$pageSize',
        method: HttpMethod.get,
      );
      
      if (response?.statusCode == 200) {
        final List<dynamic> ordersData = response?.data['orders'] ?? [];
        final orderModels = ordersData.map((json) => OrderModel.fromJson(json)).toList();
        return OrderMapper.toEntityList(orderModels);
      }
      
      // Fallback to sample data if API fails or for development
      final dataManager = getIt<DataLoadingManager>();
      final ordersData = await dataManager.loadOrders(
        customerId: customerId,
        status: status,
        page: page,
        pageSize: pageSize,
        refresh: refresh,
      );
      
      final orderModels = ordersData.map((json) => OrderModel.fromJson(json)).toList();
      return OrderMapper.toEntityList(orderModels);
    } catch (e) {
      throw Exception('Failed to fetch order history: $e');
    }
  }

  @override
  Future<OrderEntity?> getOrderDetails(String orderId) async {
    try {
      // Make API call to get order details
      final response = await ApiClient.sendHttpRequest(
        endUrl: '/get_order_details?order_id=$orderId',
        method: HttpMethod.get,
      );
      
      if (response?.statusCode == 200) {
        final orderData = response?.data;
        if (orderData == null) {
          return null;
        }
        
        final orderModel = OrderModel.fromJson(orderData);
        return OrderMapper.toEntity(orderModel);
      }
      
      // Fallback to sample data if API fails or for development
      final dataManager = getIt<DataLoadingManager>();
      final orderData = await dataManager.loadOrderDetails(orderId);
      
      if (orderData == null) {
        return null;
      }
      
      final orderModel = OrderModel.fromJson(orderData);
      return OrderMapper.toEntity(orderModel);
    } catch (e) {
      throw Exception('Failed to fetch order details: $e');
    }
  }

  @override
  Future<bool> cancelOrder(String orderId) async {
    try {
      // For now, this is a mock implementation since we're using sample data
      // In a real app, this would make an API call to cancel the order

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));

      // For sample data, we'll just return true
      // In real implementation, this would be:
      // final response = await ApiClient.sendHttpRequest(
      //   endUrl: 'orders/$orderId/cancel',
      //   method: HttpMethod.post,
      // );
      // return response?.statusCode == 200;

      return true;
    } catch (e) {
      throw Exception('Failed to cancel order: $e');
    }
  }
}
