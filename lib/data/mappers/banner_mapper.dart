import '../../domain/entities/banner_entity.dart';
import '../models/banner_model.dart';

/// Mapper to convert between HomeBanner and BannerEntity
class BannerMapper {
  /// Convert HomeBanner to BannerEntity
  static BannerEntity toEntity(HomeBanner model) {
    return BannerEntity(
      id: model.id,
      imageUrl: model.imageUrl,
      categoryId: model.categoryId,
      categoryName: model.categoryName,
    );
  }

  /// Convert BannerEntity to HomeBanner
  static HomeBanner toModel(BannerEntity entity) {
    return HomeBanner(
      id: entity.id,
      imageUrl: entity.imageUrl,
      categoryId: entity.categoryId,
      categoryName: entity.categoryName,
    );
  }

  /// Convert list of HomeBanner to list of BannerEntity
  static List<BannerEntity> toEntityList(List<HomeBanner> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  /// Convert list of BannerEntity to list of HomeBanner
  static List<HomeBanner> toModelList(List<BannerEntity> entities) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
