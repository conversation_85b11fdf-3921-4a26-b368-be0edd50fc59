import '../../domain/entities/category_entity.dart';
import '../models/category_model.dart';

/// Mapper to convert between CategoryModel and CategoryEntity
class CategoryMapper {
  /// Convert CategoryModel to CategoryEntity
  static CategoryEntity toEntity(CategoryModel model) {
    return CategoryEntity(
      id: model.id ?? '',
      name: model.name ?? '',
      count: (model.count ?? 0).toInt(),
      imageUrl: model.imageUrl,
      icon: model.icon,
      iconColor: model.iconColor != null
          ? CategoryIconColor(
              red: (model.iconColor!.red ?? 0).toInt(),
              green: (model.iconColor!.green ?? 0).toInt(),
              blue: (model.iconColor!.blue ?? 0).toInt(),
              opacity: (model.iconColor!.alpha ?? 1.0).toDouble(),
            )
          : null,
    );
  }

  /// Convert CategoryEntity to CategoryModel
  static CategoryModel toModel(CategoryEntity entity) {
    return CategoryModel(
      id: entity.id,
      name: entity.name,
      count: entity.count,
      imageUrl: entity.imageUrl,
      icon: entity.icon,
      iconColor: entity.iconColor != null
          ? IconColor(
              red: entity.iconColor!.red,
              green: entity.iconColor!.green,
              blue: entity.iconColor!.blue,
              alpha: entity.iconColor!.opacity,
            )
          : null,
    );
  }

  /// Convert list of CategoryModel to list of CategoryEntity
  static List<CategoryEntity> toEntityList(List<CategoryModel> models) {
    return models.map((model) => toEntity(model)).toList();
  }

  /// Convert list of CategoryEntity to list of CategoryModel
  static List<CategoryModel> toModelList(List<CategoryEntity> entities) {
    return entities.map((entity) => toModel(entity)).toList();
  }
}
