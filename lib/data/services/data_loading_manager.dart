import 'dart:async';
import 'package:flutter/material.dart';

import '../../features/search/services/typesense_service.dart';
import 'sample_data.dart';

/// A manager class to handle data loading for the app
class DataLoadingManager {
  final SampleDataService _dataService;
  final TypesenseService _typesenseService;

  /// Flag to determine whether to use Typesense or local data
  bool _useTypesense = true;

  /// Constructor with dependency injection
  DataLoadingManager({
    required SampleDataService dataService,
    required TypesenseService typesenseService,
  })  : _dataService = dataService,
        _typesenseService = typesenseService;

  /// Set whether to use Typesense or local data
  void setUseTypesense(bool useTypesense) {
    _useTypesense = useTypesense;
  }

  /// Load categories with pagination
  Future<List<Map<String, dynamic>>> loadCategories({
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
  }) async {
    if (_useTypesense) {
      // Typesense uses 1-based pagination
      return await _typesenseService.searchCategories(
        query: query,
        page: page + 1,
        pageSize: pageSize,
      );
    } else {
      final categories = await _dataService.getCategories();

      // Apply pagination
      final start = page * pageSize;
      final end = start + pageSize;

      if (start >= categories.length) {
        return [];
      }

      return categories.sublist(
          start, end > categories.length ? categories.length : end);
    }
  }

  /// Load subcategories with pagination
  Future<List<Map<String, dynamic>>> loadSubCategories({
    String categoryID = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
    String query = '',
  }) async {
    if (_useTypesense) {
      // Typesense uses 1-based pagination
      return await _typesenseService.searchSubCategories(
        categoryID: categoryID,
        query: query,
        page: page + 1,
        pageSize: pageSize,
      );
    } else {
      final subCategories =
          await _dataService.getSubCategories(categoryID: categoryID);

      // Apply pagination
      final start = page * pageSize;
      final end = start + pageSize;

      if (start >= subCategories.length) {
        return [];
      }

      return subCategories.sublist(
          start, end > subCategories.length ? subCategories.length : end);
    }
  }

  /// Load products with pagination
  Future<List<Map<String, dynamic>>> loadProducts(
      {String subCategoryID = '',
      int page = 0,
      int pageSize = 10,
      bool refresh = false,
      String query = '',
      String categoryID = ''}) async {
    if (_useTypesense) {
      // Typesense uses 1-based pagination
      return await _typesenseService.searchProducts(
        subCategoryID: subCategoryID,
        categoryID: categoryID,
        query: query,
        page: page + 1,
        pageSize: pageSize,
      );
    } else {
      final products =
          await _dataService.getProducts(subCategoryID: subCategoryID);

      // Apply pagination
      final start = page * pageSize;
      final end = start + pageSize;

      if (start >= products.length) {
        return [];
      }

      return products.sublist(
          start, end > products.length ? products.length : end);
    }
  }

  /// Load featured products with pagination
  Future<List<Map<String, dynamic>>> loadFeaturedProducts({
    int page = 0,
    int pageSize = 9,
    bool refresh = false,
    String query = '',
  }) async {
    if (_useTypesense) {
      // Typesense uses 1-based pagination
      return await _typesenseService.searchProducts(
        query: query,
        page: page + 1,
        pageSize: pageSize,
      );
    } else {
      // In a real app, this would call a specific API for featured products
      // For now, we'll just use the regular products
      final products = await _dataService.getProducts();

      // Apply pagination
      final start = page * pageSize;
      final end = start + pageSize;

      if (start >= products.length) {
        return [];
      }

      return products.sublist(
          start, end > products.length ? products.length : end);
    }
  }

  /// Load banners
  Future<List<Map<String, dynamic>>> getBanners() async {
    return await _dataService.getBanners();
  }

  /// Load orders with pagination
  Future<List<Map<String, dynamic>>> loadOrders({
    String customerId = 'customer_123',
    String status = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
  }) async {
    // For now, we only use local data since Typesense doesn't have orders
    final orders = await _dataService.getOrders(
      customerId: customerId,
      status: status,
    );

    // Apply pagination
    final start = page * pageSize;
    final end = start + pageSize;

    if (start >= orders.length) {
      return [];
    }

    return orders.sublist(start, end > orders.length ? orders.length : end);
  }

  /// Load order details by ID
  Future<Map<String, dynamic>?> loadOrderDetails(String orderId) async {
    return await _dataService.getOrderById(orderId);
  }
}

/// A widget that provides the DataLoadingManager to its descendants
class DataLoadingProvider extends InheritedWidget {
  final DataLoadingManager dataLoadingManager;

  const DataLoadingProvider({
    super.key,
    required this.dataLoadingManager,
    required super.child,
  });

  static DataLoadingManager of(BuildContext context) {
    final provider =
        context.dependOnInheritedWidgetOfExactType<DataLoadingProvider>();
    assert(provider != null, 'No DataLoadingProvider found in context');
    return provider!.dataLoadingManager;
  }

  @override
  bool updateShouldNotify(DataLoadingProvider oldWidget) {
    return dataLoadingManager != oldWidget.dataLoadingManager;
  }
}
